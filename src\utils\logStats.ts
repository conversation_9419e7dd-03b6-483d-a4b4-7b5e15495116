// 日志统计计算工具函数

import type { LogEntry, LogStats, BadgeType } from "@/types/log";

/**
 * 计算日志统计信息
 * 统计各类别数量、级别分布、线程数、时间范围、事件密度等
 */
export const calculateStats = (logs: LogEntry[]): LogStats => {
  const stats: LogStats = {
    total: logs.length,
    trace: 0,
    system: 0,
    ui: 0,
    errors: 0,
    warnings: 0,
    info: 0,
    debug: 0,
    threads: 0,
    timeRange: {
      start: "",
      end: "",
      duration: 0,
    },
    eventDensity: 0,
  };

  if (logs.length === 0) return stats;

  const threadIds = new Set<string>();
  const levels: { [key: string]: number } = {};
  const categories: { [key: string]: number } = {};

  logs.forEach((log) => {
    // 统计线程数
    threadIds.add(log.threadId);

    // 统计级别
    const level = log.level.toLowerCase();
    levels[level] = (levels[level] || 0) + 1;

    // 统计自动分类
    const category = log.autoCategory.toLowerCase();
    categories[category] = (categories[category] || 0) + 1;
  });

  stats.threads = threadIds.size;
  stats.errors = levels["error"] || 0;
  stats.warnings = levels["warn"] || levels["warning"] || 0;
  stats.info = levels["info"] || 0;
  stats.debug = levels["debug"] || 0;
  stats.trace = categories["trace"] || 0;
  stats.system = categories["system"] || 0;
  stats.ui = categories["ui"] || 0;

  // 计算时间范围
  if (logs.length > 0) {
    stats.timeRange.start = logs[0].timestamp;
    stats.timeRange.end = logs[logs.length - 1].timestamp;

    // 计算持续时间
    const startTime = new Date(logs[0].timestamp.replace(" ", "T")).getTime();
    const endTime = new Date(logs[logs.length - 1].timestamp.replace(" ", "T")).getTime();

    stats.timeRange.duration = endTime - startTime;

    // 计算事件密度（每秒事件数）
    if (stats.timeRange.duration > 0) {
      stats.eventDensity = stats.total / (stats.timeRange.duration / 1000);
    }
  }

  return stats;
};

/**
 * 获取日志级别对应的样式类名
 * 根据日志级别返回对应的颜色样式
 */
export const getLevelColor = (level: string): string => {
  switch (level.toUpperCase()) {
    case "ERROR":
      return "bg-red-900 text-red-300 border-red-700";
    case "WARN":
    case "WARNING":
      return "bg-yellow-900 text-yellow-300 border-yellow-700";
    case "INFO":
      return "bg-blue-900 text-blue-300 border-blue-700";
    case "DEBUG":
      return "bg-gray-800 text-gray-300 border-gray-600";
    case "TRACE":
      return "bg-purple-900 text-purple-300 border-purple-700";
    default:
      return "bg-gray-800 text-gray-300 border-gray-600";
  }
};

/**
 * 获取日志分类对应的样式类名
 * 根据自动分类返回对应的颜色样式
 */
export const getCategoryColor = (category: string): string => {
  switch (category) {
    case "Trace":
      return "bg-purple-900 text-purple-200 border-purple-600";
    case "System":
      return "bg-blue-900 text-blue-200 border-blue-600";
    case "UI":
      return "bg-green-900 text-green-200 border-green-600";
    default:
      return "bg-gray-900 text-gray-200 border-gray-600";
  }
};

/**
 * 获取徽章样式类名
 * 根据徽章类型返回对应的颜色样式
 */
export const getBadgeColor = (type: BadgeType): string => {
  switch (type) {
    case "error":
      return "bg-red-600 text-red-100";
    case "warning":
      return "bg-yellow-600 text-yellow-100";
    case "primary":
      return "bg-blue-600 text-blue-100";
    default:
      return "bg-gray-600 text-gray-100";
  }
};

/**
 * 格式化持续时间
 * 将毫秒数转换为易读的时间格式
 */
export const formatDuration = (milliseconds: number): string => {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  
  const seconds = Math.floor(milliseconds / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    const remainingSeconds = seconds % 60;
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

/**
 * 格式化数字显示
 * 添加千分位分隔符
 */
export const formatNumber = (num: number): string => {
  return num.toLocaleString();
};

/**
 * 计算日志分布统计
 * 按小时统计日志数量分布
 */
export const calculateLogDistribution = (logs: LogEntry[]): { hour: string; count: number }[] => {
  const distribution: { [key: string]: number } = {};
  
  logs.forEach((log) => {
    try {
      const timestamp = new Date(log.timestamp.replace(" ", "T"));
      const hour = timestamp.getHours().toString().padStart(2, "0");
      const key = `${hour}:00`;
      distribution[key] = (distribution[key] || 0) + 1;
    } catch (error) {
      // 忽略无效时间戳
    }
  });

  // 生成24小时的数据
  const result: { hour: string; count: number }[] = [];
  for (let i = 0; i < 24; i++) {
    const hour = i.toString().padStart(2, "0") + ":00";
    result.push({
      hour,
      count: distribution[hour] || 0,
    });
  }

  return result;
};

/**
 * 计算错误率
 * 计算错误和警告日志在总日志中的占比
 */
export const calculateErrorRate = (stats: LogStats): {
  errorRate: number;
  warningRate: number;
  totalIssueRate: number;
} => {
  if (stats.total === 0) {
    return {
      errorRate: 0,
      warningRate: 0,
      totalIssueRate: 0,
    };
  }

  const errorRate = (stats.errors / stats.total) * 100;
  const warningRate = (stats.warnings / stats.total) * 100;
  const totalIssueRate = ((stats.errors + stats.warnings) / stats.total) * 100;

  return {
    errorRate: Math.round(errorRate * 100) / 100,
    warningRate: Math.round(warningRate * 100) / 100,
    totalIssueRate: Math.round(totalIssueRate * 100) / 100,
  };
};

/**
 * 获取最活跃的线程
 * 返回日志数量最多的前N个线程
 */
export const getTopThreads = (logs: LogEntry[], limit: number = 5): { threadId: string; count: number }[] => {
  const threadCounts: { [key: string]: number } = {};
  
  logs.forEach((log) => {
    threadCounts[log.threadId] = (threadCounts[log.threadId] || 0) + 1;
  });

  return Object.entries(threadCounts)
    .map(([threadId, count]) => ({ threadId, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
};

/**
 * 获取最活跃的模块
 * 返回日志数量最多的前N个模块
 */
export const getTopModules = (logs: LogEntry[], limit: number = 5): { module: string; count: number }[] => {
  const moduleCounts: { [key: string]: number } = {};
  
  logs.forEach((log) => {
    if (log.module !== "Unknown") {
      moduleCounts[log.module] = (moduleCounts[log.module] || 0) + 1;
    }
  });

  return Object.entries(moduleCounts)
    .map(([module, count]) => ({ module, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
};

/**
 * 检查是否需要显示警告
 * 根据统计数据判断是否需要显示性能或错误警告
 */
export const getHealthWarnings = (stats: LogStats): string[] => {
  const warnings: string[] = [];
  
  const errorRates = calculateErrorRate(stats);
  
  // 错误率过高警告
  if (errorRates.errorRate > 5) {
    warnings.push(`错误率过高: ${errorRates.errorRate}%`);
  }
  
  // 警告率过高
  if (errorRates.warningRate > 10) {
    warnings.push(`警告率过高: ${errorRates.warningRate}%`);
  }
  
  // 事件密度过高
  if (stats.eventDensity > 100) {
    warnings.push(`事件密度过高: ${stats.eventDensity.toFixed(1)} 条/秒`);
  }
  
  // 线程数过多
  if (stats.threads > 20) {
    warnings.push(`线程数较多: ${stats.threads} 个`);
  }

  return warnings;
}; 