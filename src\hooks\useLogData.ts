// 日志数据处理Hook

import { useState, useCallback, useRef } from "react";
import type { LogEntry, LogStats } from "@/types/log";
import { parseLogContent, validateLogFormat } from "@/utils/logParser";
import { calculateStats } from "@/utils/logStats";
import { exportLogsAsCSV } from "@/utils/logExport";
import { MAX_FILE_SIZE, SUPPORTED_EXTENSIONS } from "@/constants/log";

interface UseLogDataReturn {
  // 数据状态
  logs: LogEntry[];
  stats: LogStats | null;
  
  // 加载状态
  isLoading: boolean;
  isProcessing: boolean;
  loadingMessage: string;
  error: string | null;
  
  // 操作方法
  handleFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  downloadAndProcessLog: (url: string) => Promise<void>;
  processLogContent: (content: string) => Promise<void>;
  handleExport: () => void;
  clearError: () => void;
  resetData: () => void;
  
  // 文件输入引用
  fileInputRef: React.RefObject<HTMLInputElement>;
}

/**
 * 日志数据处理Hook
 * 提供日志文件上传、解析、统计等功能
 */
export const useLogData = (): UseLogDataReturn => {
  // 状态管理
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [stats, setStats] = useState<LogStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [loadingMessage, setLoadingMessage] = useState("");
  const [error, setError] = useState<string | null>(null);
  
  // 文件输入引用
  const fileInputRef = useRef<HTMLInputElement>(null);

  /**
   * 清除错误信息
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * 重置所有数据
   */
  const resetData = useCallback(() => {
    setLogs([]);
    setStats(null);
    setError(null);
    setLoadingMessage("");
    setIsLoading(false);
    setIsProcessing(false);
  }, []);

  /**
   * 验证文件
   * 检查文件类型和大小是否符合要求
   */
  const validateFile = useCallback((file: File): { valid: boolean; error?: string } => {
    // 检查文件类型
    const hasValidExtension = SUPPORTED_EXTENSIONS.some(ext => 
      file.name.toLowerCase().endsWith(ext)
    );
    
    if (!hasValidExtension) {
      return {
        valid: false,
        error: `请选择 ${SUPPORTED_EXTENSIONS.join(" 或 ")} 格式的日志文件`
      };
    }

    // 检查文件大小
    if (file.size > MAX_FILE_SIZE) {
      const maxSizeMB = MAX_FILE_SIZE / (1024 * 1024);
      return {
        valid: false,
        error: `文件大小不能超过 ${maxSizeMB}MB`
      };
    }

    return { valid: true };
  }, []);

  /**
   * 处理文件上传
   * 验证文件并读取内容进行处理
   */
  const handleFileUpload = useCallback(async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件
    const validation = validateFile(file);
    if (!validation.valid) {
      setError(validation.error || "文件验证失败");
      return;
    }

    setIsLoading(true);
    setLoadingMessage("正在读取日志文件...");
    setError(null);

    try {
      const content = await file.text();
      await processLogContent(content);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "读取日志文件时出现未知错误"
      );
      console.error("读取日志文件失败:", err);
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  }, [validateFile]);

  /**
   * 下载远程日志文件
   * 从URL下载日志文件并进行处理
   */
  const downloadAndProcessLog = useCallback(async (url: string) => {
    setIsLoading(true);
    setLoadingMessage("正在下载日志文件...");
    setError(null);

    try {
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`);
      }

      // 检查Content-Type
      const contentType = response.headers.get("content-type");
      if (contentType && !contentType.includes("text/")) {
        console.warn("警告: 文件类型可能不是文本格式", contentType);
      }

      const logContent = await response.text();
      await processLogContent(logContent);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "下载日志文件时出现未知错误"
      );
      console.error("下载日志文件失败:", err);
    } finally {
      setIsLoading(false);
      setLoadingMessage("");
    }
  }, []);

  /**
   * 处理日志内容
   * 解析日志文本并计算统计信息
   */
  const processLogContent = useCallback(async (content: string) => {
    if (!content.trim()) {
      setError("日志文件内容为空");
      return;
    }

    // 验证日志格式
    if (!validateLogFormat(content)) {
      setError("无法识别的日志格式，请检查文件内容");
      return;
    }

    setIsProcessing(true);
    setLoadingMessage("正在解析日志内容...");

    try {
      // 解析日志内容
      const parsedLogs = await parseLogContent(
        content,
        (progress, processedLines, totalLines) => {
          setLoadingMessage(
            `正在解析日志内容... (${progress}% - ${processedLines}/${totalLines} 行)`
          );
        }
      );

      if (parsedLogs.length === 0) {
        throw new Error("未能解析出有效的日志条目");
      }

      setLogs(parsedLogs);

      // 计算统计信息
      setLoadingMessage("正在计算统计信息...");
      const logStats = calculateStats(parsedLogs);
      setStats(logStats);

      setLoadingMessage("解析完成！");
      
      // 短暂显示完成信息后清除
      setTimeout(() => {
        setLoadingMessage("");
      }, 1000);

    } catch (err) {
      setError(
        err instanceof Error ? err.message : "解析日志内容时出现未知错误"
      );
      console.error("解析日志失败:", err);
    } finally {
      setIsProcessing(false);
    }
  }, []);

  /**
   * 导出日志
   * 导出当前日志数据为CSV格式
   */
  const handleExport = useCallback(() => {
    if (logs.length === 0) {
      setError("没有可导出的日志数据");
      return;
    }

    setIsLoading(true);

    try {
      exportLogsAsCSV(logs);
    } catch (err) {
      setError("导出CSV时出现错误");
      console.error("导出失败:", err);
    } finally {
      // 延迟恢复状态，给用户足够时间看到导出操作
      setTimeout(() => setIsLoading(false), 1000);
    }
  }, [logs]);

  return {
    // 数据状态
    logs,
    stats,
    
    // 加载状态
    isLoading,
    isProcessing,
    loadingMessage,
    error,
    
    // 操作方法
    handleFileUpload,
    downloadAndProcessLog,
    processLogContent,
    handleExport,
    clearError,
    resetData,
    
    // 引用
    fileInputRef,
  };
}; 