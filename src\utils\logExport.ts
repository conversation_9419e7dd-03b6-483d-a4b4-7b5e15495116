// 日志导出工具函数

import type { LogEntry, LogStats } from "@/types/log";

/**
 * 导出日志为CSV格式
 * 将过滤后的日志数据导出为CSV文件
 */
export const exportLogsAsCSV = (logs: LogEntry[], filename?: string): void => {
  try {
    // CSV文件头
    const csvHeaders = [
      "时间戳",
      "级别", 
      "线程ID",
      "模块",
      "作用域",
      "分类",
      "自动分类",
      "消息",
      "函数标签",
      "动作标签",
    ];

    // 转换数据为CSV格式
    const csvRows = logs.map((log) => [
      log.timestamp,
      log.level,
      log.threadId,
      log.module,
      log.scope,
      log.category,
      log.autoCategory,
      `"${log.message.replace(/"/g, '""')}"`, // 转义双引号
      log.tags.functions.join(";"),
      log.tags.actions.join(";"),
    ]);

    // 组合CSV内容
    const csvContent = [csvHeaders, ...csvRows]
      .map((row) => row.join(","))
      .join("\n");

    // 创建并下载文件
    const blob = new Blob(["\uFEFF" + csvContent], {
      type: "text/csv;charset=utf-8;",
    });

    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = filename || generateDefaultFilename("csv");
    link.click();

    // 清理URL对象
    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error("导出CSV失败:", error);
    throw new Error("导出CSV时出现错误");
  }
};

/**
 * 导出统计信息为JSON格式
 * 将统计数据导出为JSON文件
 */
export const exportStatsAsJSON = (stats: LogStats, logs: LogEntry[], filename?: string): void => {
  try {
    const exportData = {
      exportTime: new Date().toISOString(),
      logCount: logs.length,
      stats,
      topModules: getTopItems(logs, "module", 10),
      topThreads: getTopItems(logs, "threadId", 10),
      levelDistribution: getLevelDistribution(logs),
      categoryDistribution: getCategoryDistribution(logs),
    };

    const jsonContent = JSON.stringify(exportData, null, 2);

    const blob = new Blob([jsonContent], {
      type: "application/json;charset=utf-8;",
    });

    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = filename || generateDefaultFilename("json");
    link.click();

    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error("导出JSON失败:", error);
    throw new Error("导出统计信息时出现错误");
  }
};

/**
 * 导出日志为纯文本格式
 * 保持原始日志格式导出
 */
export const exportLogsAsText = (logs: LogEntry[], filename?: string): void => {
  try {
    const textContent = logs
      .map((log) => {
        return `${log.timestamp} ${log.level} [${log.threadId}] [${log.module}][${log.scope}][${log.category}] ${log.message}`;
      })
      .join("\n");

    const blob = new Blob([textContent], {
      type: "text/plain;charset=utf-8;",
    });

    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = filename || generateDefaultFilename("log");
    link.click();

    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error("导出文本失败:", error);
    throw new Error("导出文本文件时出现错误");
  }
};

/**
 * 生成默认文件名
 * 根据当前时间和扩展名生成文件名
 */
export const generateDefaultFilename = (extension: string): string => {
  const now = new Date();
  const timestamp = now
    .toISOString()
    .slice(0, 19)
    .replace(/:/g, "-");
  
  return `logs_export_${timestamp}.${extension}`;
};

/**
 * 获取字段的Top项目统计
 * 统计指定字段的出现频率并返回前N项
 */
const getTopItems = (
  logs: LogEntry[], 
  field: keyof LogEntry, 
  limit: number
): { name: string; count: number }[] => {
  const counts: { [key: string]: number } = {};
  
  logs.forEach((log) => {
    const value = String(log[field]);
    if (value && value !== "Unknown") {
      counts[value] = (counts[value] || 0) + 1;
    }
  });

  return Object.entries(counts)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
};

/**
 * 获取级别分布统计
 * 统计各个日志级别的数量分布
 */
const getLevelDistribution = (logs: LogEntry[]): { level: string; count: number }[] => {
  const distribution: { [key: string]: number } = {};
  
  logs.forEach((log) => {
    distribution[log.level] = (distribution[log.level] || 0) + 1;
  });

  return Object.entries(distribution)
    .map(([level, count]) => ({ level, count }))
    .sort((a, b) => b.count - a.count);
};

/**
 * 获取分类分布统计
 * 统计各个自动分类的数量分布
 */
const getCategoryDistribution = (logs: LogEntry[]): { category: string; count: number }[] => {
  const distribution: { [key: string]: number } = {};
  
  logs.forEach((log) => {
    distribution[log.autoCategory] = (distribution[log.autoCategory] || 0) + 1;
  });

  return Object.entries(distribution)
    .map(([category, count]) => ({ category, count }))
    .sort((a, b) => b.count - a.count);
};

/**
 * 验证导出数据的完整性
 * 检查要导出的数据是否有效
 */
export const validateExportData = (logs: LogEntry[]): { valid: boolean; error?: string } => {
  if (!Array.isArray(logs)) {
    return { valid: false, error: "日志数据格式无效" };
  }
  
  if (logs.length === 0) {
    return { valid: false, error: "没有可导出的日志数据" };
  }

  // 检查必要字段
  const requiredFields = ["timestamp", "level", "message"];
  const firstLog = logs[0];
  
  for (const field of requiredFields) {
    if (!(field in firstLog)) {
      return { valid: false, error: `缺少必要字段: ${field}` };
    }
  }

  return { valid: true };
};

/**
 * 获取导出选项配置
 * 返回可用的导出格式和配置
 */
export const getExportOptions = () => {
  return {
    formats: [
      {
        key: "csv",
        label: "CSV 表格",
        description: "适合在Excel中打开的表格格式",
        mimeType: "text/csv",
      },
      {
        key: "json",
        label: "JSON 数据",
        description: "包含统计信息的结构化数据",
        mimeType: "application/json",
      },
      {
        key: "text",
        label: "纯文本",
        description: "保持原始格式的文本文件",
        mimeType: "text/plain",
      },
    ],
    settings: {
      includeHeaders: true,
      includeStats: true,
      includeTagsInCSV: true,
      dateFormat: "ISO",
    },
  };
};

/**
 * 批量导出多种格式
 * 同时导出多种格式的文件
 */
export const exportMultipleFormats = async (
  logs: LogEntry[], 
  stats: LogStats, 
  formats: string[]
): Promise<void> => {
  const validation = validateExportData(logs);
  if (!validation.valid) {
    throw new Error(validation.error);
  }

  const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
  
  const exportPromises = formats.map(async (format) => {
    const filename = `logs_export_${timestamp}.${format}`;
    
    switch (format) {
      case "csv":
        return exportLogsAsCSV(logs, filename);
      case "json":
        return exportStatsAsJSON(stats, logs, filename);
      case "text":
        return exportLogsAsText(logs, filename);
      default:
        throw new Error(`不支持的导出格式: ${format}`);
    }
  });

  try {
    await Promise.all(exportPromises);
  } catch (error) {
    console.error("批量导出失败:", error);
    throw new Error("批量导出时出现错误");
  }
}; 