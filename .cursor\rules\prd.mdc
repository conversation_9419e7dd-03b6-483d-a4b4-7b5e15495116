---
alwaysApply: true
---
# XLauncher 日志分析器 Svelte 重写版 项目需求文档（PRD）

## 1. 项目背景与目标

本项目旨在基于 Svelte + TypeScript 重写现有的 XLauncher Log Analyzer（参考 `references/log-analyzer.html`），实现 Qt C++ 桌面应用日志的智能分析、可视化与高效交互，提升前端架构的现代化、响应式与可维护性。

## 2. 功能概览

- 支持本地日志文件（.log/.txt）导入与默认日志加载
- 日志自动解析、分类（Trace/System/UI）、标签提取
- 统计区实时展示关键信息（版本、deviceId、事件数、时长、密度等）
- 可视化瀑布图/时间轴，支持缩放、事件标记
- 日志表格支持多条件高级搜索、标签过滤、去除规则
- 每行日志支持高亮、快捷搜索（+/-）、线程ID重命名
- 支持导出当前结果为 CSV
- 交互体验流畅，状态提示明确

## 3. 详细需求拆分

### 3.1 页面结构

- 头部：标题、简介
- 控制区：文件选择、分类/时间过滤、默认日志加载、导出按钮
- 统计区：Trace/System/UI 事件数、总时长、事件密度等
- 可视化区：瀑布图、时间轴、重要事件标记、缩放/重置
- 日志详情区：表格、搜索、标签、去除规则、线程ID重命名、高亮
- 模态框：去除规则设置

### 3.2 日志数据处理

- 支持本地 .log/.txt 文件导入，UTF-8 编码，最大支持数 MB
- 自动解析日志格式，字段包括：时间戳、线程ID、模块、作用域、分类、消息
- 日志自动分类（Trace/System/UI），并提取函数名、模块名、动作关键词为标签
- 线程ID、模块、动作等均可作为标签用于快速筛选

### 3.3 统计与概览

- 实时统计并展示：
  - 日志总条数
  - Trace/System/UI 各类别数量
  - 线程数
  - 总时长（ms/s/m）
  - 事件密度（每秒事件数）
  - 版本号、deviceId 等关键信息（自动从日志中提取并展示于显著位置）

### 3.4 可视化分析

- 瀑布图/时间轴展示日志分布
- 支持缩放（Ctrl+滚轮）、重置（Ctrl+0）、拖动
- 重要事件自动标记（如启动、关闭、登录、异常等，规则可扩展）
- 点击瀑布图/时间轴可联动高亮日志表格行

### 3.5 日志表格与交互

- 表格字段：时间、线程ID、分类、模块、消息
- 支持表头排序、字段宽度自适应
- 每行日志旁增加 (+)（等于该字段值）和 (–)（取反该字段值）按钮，点击后自动添加/叠加搜索条件
- 支持点击行高亮，联动可视化定位
- 线程ID支持重命名，所有交互有视觉反馈

## 4. 搜索与过滤能力细则

### 4.1 基础搜索

- 每一列均可单独搜索，支持子串匹配
- 搜索不区分大小写

### 4.2 高级搜索

- 支持多条件组合（AND/OR），可指定字段（消息、模块、分类、线程ID等）
- 操作符支持：
  - 包含（包含子串）
  - 不包含
  - 等于
  - 不等于
  - 是否忽略大小写（可选）
- 支持时间范围筛选（绝对时间、启动/运行/关闭阶段）
- 标签点击可快速叠加过滤条件，支持多选
- 支持一键重置所有搜索与过滤条件

### 4.3 去除规则

- 用户可自定义去除规则（如“去除包含某关键词的消息”）
- 去除规则可启用/禁用/删除，实时生效
- 去除规则支持命名，便于管理

### 4.4 高亮功能

- 用户可自定义高亮某一行日志（如点击/标记）
- 高亮行在表格与可视化中均有明显视觉提示
- 支持取消高亮

## 5. 用户体验与可用性要求

- 所有交互均有视觉反馈（高亮、选中、禁用等）
- 加载、无数据、错误等状态有明确提示
- 支持快捷键（如 Ctrl+滚轮缩放、Ctrl+0 重置视图）
- 线程ID、标签等支持自定义与多选
- 支持导出当前过滤/搜索结果为 CSV
- 响应式设计，适配不同屏幕尺寸
- 操作直观，控件有清晰标签与提示

## 6. 可扩展性与性能建议

- 组件化开发，逻辑与展示分离
- 日志解析与渲染需高效，支持大文件（逐行处理，懒加载）
- 图表需正确销毁与重建，防止内存泄漏
- 支持后续扩展如：自定义主题、插件机制、更多可视化类型等

## 7. 其他建议

- 代码需有适当注释，复杂逻辑需详细说明
- 关键交互需有异常处理与用户提示
- 所有用户输入需校验，保证安全与稳定
- 文档需完善，便于团队协作与后续维护
