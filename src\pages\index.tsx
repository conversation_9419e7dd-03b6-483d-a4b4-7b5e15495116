// XLauncher 日志分析器主页

import type { LogLevel, LogCategory } from "@/types/log";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";

import DefaultLayout from "@/layouts/default";
import { LogToolbar } from "@/components/LogToolbar";
import { LogFilters } from "@/components/LogFilters";
import { LogTable } from "@/components/LogTable";
import { useLogData } from "@/hooks/useLogData";
import { useAdvancedSearch } from "@/hooks/useAdvancedSearch";

/**
 * XLauncher 日志分析器主页
 * 提供完整的日志分析功能：上传、解析、搜索、可视化、导出
 */
export default function IndexPage() {
  // 基础状态管理
  const [selectedLevel, setSelectedLevel] = useState<LogLevel | "ALL">("ALL");
  const [selectedCategory, setSelectedCategory] = useState<LogCategory | "ALL">(
    "ALL",
  );
  const [basicSearchValue, setBasicSearchValue] = useState("");

  // 日志数据管理
  const {
    logs,
    stats,
    isLoading,
    error,
    handleFileUpload,
    processLogContent,
    handleExport,
    resetData,
  } = useLogData();

  // 高级搜索管理
  const {
    advancedSearch,
    addSearchCondition,
    removeSearchCondition,
    updateSearchCondition,
    setTimeRange,
    applyAdvancedSearch,
    resetAdvancedSearch,
    applyAdvancedFilter,
  } = useAdvancedSearch();

  // URL参数处理
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const logUrl = urlParams.get("url");

    if (logUrl) {
      const handleUrlLoad = async () => {
        try {
          const response = await fetch(logUrl);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
          const content = await response.text();

          await processLogContent(content);
        } catch (err) {
          console.error("加载远程日志失败:", err);
        }
      };

      handleUrlLoad();
    }
  }, [processLogContent]);

  // 过滤日志
  const getFilteredLogs = () => {
    if (!logs.length) return [];

    let filtered = [...logs];

    // 应用高级搜索过滤
    filtered = applyAdvancedFilter(filtered);

    // 应用基础搜索
    if (basicSearchValue.trim()) {
      const searchTerm = basicSearchValue.toLowerCase().trim();

      filtered = filtered.filter(
        (log) =>
          log.message.toLowerCase().includes(searchTerm) ||
          log.level.toLowerCase().includes(searchTerm) ||
          log.module.toLowerCase().includes(searchTerm) ||
          log.category.toLowerCase().includes(searchTerm),
      );
    }

    // 应用级别过滤
    if (selectedLevel !== "ALL") {
      filtered = filtered.filter((log) => log.level === selectedLevel);
    }

    // 应用分类过滤
    if (selectedCategory !== "ALL") {
      filtered = filtered.filter(
        (log) => log.autoCategory === selectedCategory,
      );
    }

    return filtered;
  };

  const displayLogs = getFilteredLogs();

  // 调试信息
  console.log("原始日志数量:", logs.length);
  console.log("过滤后日志数量:", displayLogs.length);
  console.log("当前过滤条件:", {
    selectedLevel,
    selectedCategory,
    basicSearchValue,
    advancedSearchConditions: advancedSearch.conditions.length,
  });

  // 计算显示统计信息
  const displayStats = {
    total: displayLogs.length,
    error: displayLogs.filter((log) => log.level === "ERROR").length,
    warn: displayLogs.filter((log) => log.level === "WARN").length,
    trace: displayLogs.filter((log) => log.autoCategory === "Trace").length,
    system: displayLogs.filter((log) => log.autoCategory === "System").length,
    ui: displayLogs.filter((log) => log.autoCategory === "UI").length,
  };

  // 高级搜索处理函数
  const handleApplyAdvancedSearch = () => {
    applyAdvancedSearch();
  };

  const handleClearAdvancedSearch = () => {
    resetAdvancedSearch();
  };

  return (
    <DefaultLayout>
      <div className="min-h-screen bg-gray-900 text-gray-100">
        {/* 主容器 */}
        <motion.div
          animate={{ opacity: 1 }}
          className="flex h-screen"
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* 左侧筛选器 */}
          <LogFilters
            advancedSearch={advancedSearch}
            selectedCategory={selectedCategory}
            selectedLevel={selectedLevel}
            stats={displayStats}
            onAddCondition={addSearchCondition}
            onApplySearch={handleApplyAdvancedSearch}
            onCategoryChange={setSelectedCategory}
            onClearAdvancedSearch={handleClearAdvancedSearch}
            onLevelChange={setSelectedLevel}
            onRemoveCondition={removeSearchCondition}
            onSetTimeRange={setTimeRange}
            onUpdateCondition={updateSearchCondition}
          />

          {/* 右侧主内容区 */}
          <div className="flex-1 flex flex-col">
            {/* 顶部工具栏 */}
            <LogToolbar
              basicSearchValue={basicSearchValue}
              canExport={displayLogs.length > 0}
              error={error}
              isLoading={isLoading}
              onBasicSearchChange={setBasicSearchValue}
              onExport={handleExport}
              onFileUpload={handleFileUpload}
              onReset={resetData}
            />

            {/* 日志表格区域 */}
            <div className="flex-1 overflow-hidden">
              <LogTable isLoading={isLoading} logs={displayLogs} />
            </div>
          </div>
        </motion.div>
      </div>
    </DefaultLayout>
  );
}
