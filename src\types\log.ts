// 日志相关类型定义

// 搜索条件接口定义
export interface SearchCondition {
  id: string;
  field: string; // 搜索字段：message, module, category, threadId, level
  operator: string; // 操作符：contains, not_contains, equals, not_equals
  value: string; // 搜索值
  caseSensitive: boolean; // 是否区分大小写
}

// 高级搜索配置接口
export interface AdvancedSearchConfig {
  conditions: SearchCondition[];
  logicOperator: "AND" | "OR"; // 多条件间的逻辑关系
  timeRange: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

// 日志条目接口定义
export interface LogEntry {
  id: number;
  timestamp: string;
  rawTimestamp: string;
  threadId: string;
  level: string;
  module: string;
  scope: string;
  category: string;
  message: string;
  autoCategory: "Trace" | "System" | "UI";
  tags: {
    functions: string[];
    modules: string[];
    actions: string[];
  };
}

// 日志统计信息接口
export interface LogStats {
  total: number;
  trace: number;
  system: number;
  ui: number;
  errors: number;
  warnings: number;
  info: number;
  debug: number;
  threads: number;
  timeRange: {
    start: string;
    end: string;
    duration: number; // 毫秒
  };
  eventDensity: number; // 事件数/秒
}

// 搜索字段选项类型
export interface SearchFieldOption {
  key: string;
  label: string;
}

// 搜索操作符选项类型
export interface SearchOperatorOption {
  key: string;
  label: string;
}

// 徽章类型
export type BadgeType = "error" | "warning" | "primary" | "default";

// 日志级别类型
export type LogLevel = "ALL" | "ERROR" | "WARN" | "INFO" | "DEBUG" | "TRACE";

// 日志分类类型
export type LogCategory = "ALL" | "Trace" | "System" | "UI"; 