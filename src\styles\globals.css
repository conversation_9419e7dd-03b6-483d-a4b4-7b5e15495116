@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义滚动条样式 - 适配深色主题 */
@layer base {
  /* Webkit滚动条样式 (Chrome, Safari, Edge) */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #1f2937; /* gray-800 */
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #4b5563; /* gray-600 */
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #6b7280; /* gray-500 */
  }

  ::-webkit-scrollbar-thumb:active {
    background: #9ca3af; /* gray-400 */
  }

  /* 滚动条角落 */
  ::-webkit-scrollbar-corner {
    background: #1f2937; /* gray-800 */
  }

  /* Firefox滚动条样式 */
  * {
    scrollbar-width: thin;
    scrollbar-color: #4b5563 #1f2937; /* thumb track */
  }

  /* 特殊区域滚动条 - 更细的滚动条 */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #374151; /* gray-700 */
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #6b7280; /* gray-500 */
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #9ca3af; /* gray-400 */
  }

  /* 高亮滚动条 - 用于重要内容区域 */
  .scrollbar-accent::-webkit-scrollbar-thumb {
    background: #3b82f6; /* blue-500 */
  }

  .scrollbar-accent::-webkit-scrollbar-thumb:hover {
    background: #60a5fa; /* blue-400 */
  }

  .scrollbar-accent::-webkit-scrollbar-thumb:active {
    background: #2563eb; /* blue-600 */
  }

  /* 隐藏滚动条但保持功能 */
  .scrollbar-hide {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 透明滚动条 - 用于叠加内容 */
  .scrollbar-overlay::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-overlay::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-overlay::-webkit-scrollbar-thumb {
    background: rgba(75, 85, 99, 0.6); /* gray-600 with opacity */
    border-radius: 3px;
  }

  .scrollbar-overlay::-webkit-scrollbar-thumb:hover {
    background: rgba(107, 114, 128, 0.8); /* gray-500 with opacity */
  }

  /* HeroUI 组件滚动条样式 */
  [data-slot="base"]::-webkit-scrollbar,
  [data-slot="listbox"]::-webkit-scrollbar,
  .heroui-dropdown-content::-webkit-scrollbar,
  .heroui-listbox::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  [data-slot="base"]::-webkit-scrollbar-track,
  [data-slot="listbox"]::-webkit-scrollbar-track,
  .heroui-dropdown-content::-webkit-scrollbar-track,
  .heroui-listbox::-webkit-scrollbar-track {
    background: #374151; /* gray-700 */
    border-radius: 3px;
  }

  [data-slot="base"]::-webkit-scrollbar-thumb,
  [data-slot="listbox"]::-webkit-scrollbar-thumb,
  .heroui-dropdown-content::-webkit-scrollbar-thumb,
  .heroui-listbox::-webkit-scrollbar-thumb {
    background: #6b7280; /* gray-500 */
    border-radius: 3px;
  }

  [data-slot="base"]::-webkit-scrollbar-thumb:hover,
  [data-slot="listbox"]::-webkit-scrollbar-thumb:hover,
  .heroui-dropdown-content::-webkit-scrollbar-thumb:hover,
  .heroui-listbox::-webkit-scrollbar-thumb:hover {
    background: #9ca3af; /* gray-400 */
  }

  /* 表格滚动条增强 */
  .table-scroll::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  .table-scroll::-webkit-scrollbar-track {
    background: #1f2937; /* gray-800 */
    border-radius: 5px;
  }

  .table-scroll::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3b82f6, #60a5fa); /* blue gradient */
    border-radius: 5px;
    border: 1px solid #1f2937;
  }

  .table-scroll::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2563eb, #3b82f6); /* darker blue gradient */
  }

  /* 平滑滚动 */
  html {
    scroll-behavior: smooth;
  }

  /* 滚动区域基础样式 */
  .scroll-area {
    overflow: auto;
    scrollbar-width: thin;
    scrollbar-color: #4b5563 #1f2937;
  }
}
