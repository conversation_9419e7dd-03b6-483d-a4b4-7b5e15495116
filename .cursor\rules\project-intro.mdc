---
alwaysApply: true
---
# 项目背景
XLauncher 日志分析器是一款基于 Web 的智能日志分析工具，专为 Qt C++ 桌面应用日志设计，具备智能分类、高级搜索与可视化分析能力，适用于开发、测试与运维场景。

## 开发规范
- **代码行数**：每个文件的代码行数不超过 200 行。请使用模块化管理代码，每个模块的代码行数不超过 200 行。
- **模块管理架构**：须按照专业的模块分类进行管理，保持结构简洁。
- **模块化 JavaScript**：应采用 ES6 类，按逻辑方法组织代码。
- **响应式设计**：须确保界面在不同屏幕尺寸下均能良好展示。
- **专业化样式**：建议使用现代 CSS（如渐变、阴影、平滑过渡等）提升视觉体验。
- **页面改动强制测试流程**：每次修改时，须使用 MCP 浏览器访问 http://localhost:5179 进行功能与显示验证。操作步骤：保存更改后，立即在 MCP 浏览器中访问本地服务，检查页面功能与样式是否正常，验证无误后方可提交。

## 日志处理规范
- **格式要求**：须支持 Qt 日志格式：`YYYYMMDD-HHMMSS.mmm<INFO :THREADID>: <Info> [MODULE][SCOPE][CATEGORY] MESSAGE`
- **类别自动分类**：日志应自动分为：
  - `Trace`：应用生命周期事件
  - `System`：系统信息与配置
  - `UI`：用户界面交互
- **标签提取**：自动提取并分类：
  - 函数名（绿色标签）
  - 模块名（蓝色标签）
  - 动作关键词（橙色标签）

## 搜索功能规范
- **多条件支持**：须支持多条件, "包含" / "不包含" / "等于" / "不等于", 以及忽略大小写 (默认忽略大小写)
- **字段指定搜索**：可按消息、模块、类别、线程 ID 等字段精确搜索。
- **标签过滤**：支持通过点击标签快速筛选。
- **实时更新**：搜索条件变更时，结果应即时刷新。

## 用户体验规范
- **自动加载**：页面加载时自动载入默认日志数据。
- **直观操作**：控件须有清晰标签与提示。
- **反馈机制**：应显示加载状态与错误信息。
- **导出功能**：支持将数据导出为 CSV 格式。

## 技术要求
- **依赖库**：须使用 Chart.js 及其日期适配器。
- **浏览器兼容性**：支持现代浏览器（ES6+）。
- **高效解析**：日志应逐行处理，避免阻塞界面。
- **内存管理**：须正确销毁和重建图表，防止内存泄漏。
- **懒加载**：仅处理可见数据。

## 错误处理
- **容错降级**：须能处理缺失或格式错误的日志。
- **用户提示**：出现错误时应有明确提示。
- **恢复机制**：用户可在无需刷新页面的情况下恢复。

## 使用规范
- **文件输入**：支持 .log 和 .txt，须为 UTF-8 编码，最大支持数 MB 文件。
- **搜索语法**：搜索须不区分大小写，支持子串匹配、AND/OR 逻辑、字段选择。
- **过滤组合**：多重过滤条件应叠加生效，须有一键重置与可视化反馈。

## 质量保证
- **浏览器测试**：须在主流浏览器（Chrome、Firefox、Safari、Edge）测试。
- **设备测试**：须在桌面与平板设备验证。
- **性能测试**：须确保大日志文件下流畅运行。
- **功能测试**：须验证所有搜索与过滤组合。
- **代码质量**：保持格式统一、命名规范，复杂逻辑须有注释，函数应聚焦且可复用，关键处须有异常处理。

## 安全要求
- **本地处理**：所有文件须在浏览器本地处理，禁止上传服务器。
- **输入校验**：所有用户输入须校验。
- **数据隐私**：仅在必要时本地存储数据，禁止收集用户信息，用户须有数据控制权。

## 扩展与维护
- **新功能兼容性**：新增功能不得破坏现有特性。
- **渐进增强**：建议以增强方式扩展功能。
- **用户测试**：新功能须经真实用户测试。
- **自定义与插件**：支持颜色、格式、行为自定义，建议考虑插件架构与主题支持。
- **开发流程**：建议测试驱动、增量开发、定期收集用户反馈。
- **文档要求**：复杂算法须注释，用户与 API 文档须完善。
- **维护**：依赖须定期更新，性能持续监控，须有缺陷跟踪机制。 


