// 日志工具栏组件

import React from "react";
import { But<PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import { motion } from "framer-motion";
import { 
  titleVariants, 
  dividerVariants, 
  toolbarItemVariants,
  buttonVariants,
  loadingSpinnerVariants 
} from "@/animations/variants";

interface LogToolbarProps {
  // 搜索相关
  basicSearchValue: string;
  onBasicSearchChange: (value: string) => void;
  
  // 文件操作
  fileName?: string;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  onExport: () => void;
  canExport: boolean;
  onReset: () => void;
  
  // 状态
  isLoading: boolean;
  error: string | null;
}

/**
 * 日志工具栏组件
 * 提供标题、基础搜索、文件上传、导出等功能
 */
export const LogToolbar: React.FC<LogToolbarProps> = ({
  basicSearchValue,
  onBasicSearchChange,
  fileName,
  onFileUpload,
  onExport,
  canExport,
  onReset,
  isLoading,
  error,
}) => {
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const handleFileClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <motion.div 
      className="bg-gray-800 border-b border-gray-700 p-4"
      variants={titleVariants}
      initial="hidden"
      animate="visible"
    >
      {/* 隐藏的文件输入 */}
      <input
        ref={fileInputRef}
        accept=".log,.txt"
        className="hidden"
        type="file"
        onChange={onFileUpload}
      />

      <div className="space-y-4">
        {/* 标题行 */}
        <motion.div 
          className="flex items-center justify-between"
          variants={toolbarItemVariants}
        >
          <h1 className="text-2xl font-bold text-blue-400">
            XLauncher 日志分析器
          </h1>
          
          {fileName && (
            <motion.div 
              className="text-sm text-gray-400"
              variants={toolbarItemVariants}
            >
              文件: {fileName}
            </motion.div>
          )}
        </motion.div>

        {/* 分隔线 */}
        <motion.div 
          className="h-px bg-gray-700"
          variants={dividerVariants}
        />

        {/* 工具栏 */}
        <motion.div 
          className="flex items-center gap-4 flex-wrap"
          variants={toolbarItemVariants}
        >
          {/* 文件上传按钮 */}
          <motion.div variants={buttonVariants} whileHover="hover" whileTap="tap">
            <Button
              className="bg-blue-700 text-blue-100 hover:bg-blue-600"
              isLoading={isLoading}
              onPress={handleFileClick}
            >
              📁 上传日志
            </Button>
          </motion.div>

          {/* 基础搜索 */}
          <motion.div 
            className="flex-1 max-w-md"
            variants={toolbarItemVariants}
          >
            <Input
              classNames={{
                input: "bg-gray-700 text-gray-100",
                inputWrapper: "bg-gray-700 border-gray-600",
              }}
              placeholder="搜索日志内容..."
              value={basicSearchValue}
              onValueChange={onBasicSearchChange}
            />
          </motion.div>

          {/* 导出按钮 */}
          <motion.div variants={buttonVariants} whileHover="hover" whileTap="tap">
            <Button
              className="bg-green-700 text-green-100 hover:bg-green-600"
              isDisabled={!canExport}
              onPress={onExport}
            >
              📥 导出CSV
            </Button>
          </motion.div>

          {/* 重置按钮 */}
          <motion.div variants={buttonVariants} whileHover="hover" whileTap="tap">
            <Button
              className="bg-red-700 text-red-100 hover:bg-red-600"
              variant="bordered"
              onPress={onReset}
            >
              🗑️ 清空
            </Button>
          </motion.div>

          {/* 加载状态 */}
          {isLoading && (
            <motion.div 
              className="flex items-center gap-2 text-blue-400"
              variants={loadingSpinnerVariants}
              animate="animate"
            >
              <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
              <span className="text-sm">加载中...</span>
            </motion.div>
          )}
        </motion.div>

        {/* 错误提示 */}
        {error && (
          <motion.div 
            className="bg-red-900/50 border border-red-700 rounded-lg p-3"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-red-400">⚠️</span>
                <span className="text-red-300 text-sm">{error}</span>
              </div>
              <Button
                className="text-red-400 hover:text-red-300"
                size="sm"
                variant="light"
                onPress={() => {/* 可以添加清除错误的功能 */}}
              >
                ✕
              </Button>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}; 