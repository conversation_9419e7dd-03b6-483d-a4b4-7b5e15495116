// 日志表格组件

import type { LogEntry } from "@/types/log";

import React from "react";
import { Code } from "@heroui/code";
import { motion, AnimatePresence } from "framer-motion";

import { getLevelColor, getCategoryColor } from "@/utils/logStats";
import { logRowVariants, itemVariants } from "@/animations/variants";

interface LogTableProps {
  logs: LogEntry[];
  isLoading: boolean;
}

/**
 * 日志表格组件
 * 显示日志数据，包含级别、线程、模块、分类、消息等信息
 */
export const LogTable: React.FC<LogTableProps> = ({ logs, isLoading }) => {
  const displayLogs = logs;
  const hasMoreLogs = false;

  // 空状态显示
  if (logs.length === 0 && !isLoading) {
    return (
      <div className="h-full bg-gray-900 flex flex-col">
        <motion.div
          className="p-4 border-b border-gray-700"
          variants={itemVariants}
        >
          <h3 className="text-lg font-semibold text-gray-100">日志记录</h3>
        </motion.div>

        <motion.div
          className="flex-1 flex items-center justify-center min-h-0"
          variants={itemVariants}
        >
          <div className="text-center text-gray-400">
            <div className="text-6xl mb-4">📋</div>
            <h3 className="text-xl font-semibold mb-2">暂无日志数据</h3>
            <p className="text-sm">请上传日志文件或通过URL参数加载远程日志</p>
            <p className="text-xs mt-2 text-gray-500">
              支持格式: .log, .txt | 最大大小: 10MB
            </p>
          </div>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="h-full bg-gray-900 flex flex-col">
      {/* 表头 */}
      <motion.div
        className="p-4 border-b border-gray-700"
        variants={itemVariants}
      >
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold text-gray-100">日志记录</h3>
          <div className="flex items-center gap-2">
            <span className="px-2 py-1 bg-gray-700 text-gray-300 rounded text-sm">
              共 {logs.length.toLocaleString()} 条日志
            </span>
          </div>
        </div>
      </motion.div>

      {/* 表格 */}
      <div className="flex-1 overflow-auto table-scroll min-h-0">
        <table className="w-full table-fixed">
          {/* 表头 */}
          <motion.thead
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-800 border-b border-gray-700 sticky top-0"
            initial={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5, delay: 0.4 }}
          >
            <tr>
              <th className="px-2 py-2 text-left text-xs font-medium text-gray-300 w-32">
                时间
              </th>
              <th className="px-2 py-2 text-left text-xs font-medium text-gray-300 w-16">
                级别
              </th>
              <th className="px-2 py-2 text-left text-xs font-medium text-gray-300 w-32">
                线程
              </th>
              <th className="px-2 py-2 text-left text-xs font-medium text-gray-300 w-24">
                模块
              </th>
              <th className="px-2 py-2 text-left text-xs font-medium text-gray-300 w-16">
                分类
              </th>
              <th className="px-2 py-2 text-left text-xs font-medium text-gray-300 w-auto">
                消息
              </th>
            </tr>
          </motion.thead>

          {/* 表体 */}
          <tbody>
            <AnimatePresence>
              {displayLogs.map((log, index) => (
                <LogRow key={log.id} index={index} log={log} />
              ))}
            </AnimatePresence>
          </tbody>
        </table>
      </div>
    </div>
  );
};

/**
 * 日志行组件
 * 单独提取出来以优化性能和动画
 */
const LogRow: React.FC<{ log: LogEntry; index: number }> = React.memo(
  ({ log, index }) => {
    return (
      <motion.tr
        animate="visible"
        className="border-b border-gray-800 cursor-pointer"
        initial="hidden"
        transition={{ delay: index * 0.05 }}
        variants={logRowVariants}
      >
        {/* 时间戳 */}
        <td className="px-2 py-2 font-mono text-xs text-gray-400 w-32">
          <div className="truncate">{log.timestamp}</div>
        </td>

        {/* 级别 */}
        <td className="px-2 py-2 w-16">
          <span
            className={`px-1 py-0.5 rounded text-xs border ${getLevelColor(log.level)}`}
          >
            {log.level}
          </span>
        </td>

        {/* 线程ID */}
        <td className="px-2 py-2 w-32">
          <Code className="text-xs bg-gray-700 text-gray-300 font-mono">
            {log.threadId}
          </Code>
        </td>

        {/* 模块 */}
        <td className="px-2 py-2 w-24">
          <Code className="text-xs bg-gray-700 text-blue-300 truncate">
            {log.module}
          </Code>
        </td>

        {/* 自动分类 */}
        <td className="px-2 py-2 w-16">
          <span
            className={`px-1 py-0.5 rounded text-xs border ${getCategoryColor(log.autoCategory)}`}
          >
            {log.autoCategory}
          </span>
        </td>

        {/* 消息和标签 */}
        <td className="px-2 py-2">
          <div className="w-full">
            <div className="text-gray-200 mb-1 break-words whitespace-pre-wrap font-mono text-sm leading-relaxed w-full">
              {log.message}
            </div>

            {/* 标签 */}
            {(log.tags?.functions?.length > 0 ||
              log.tags?.actions?.length > 0) && (
              <div className="flex gap-1 flex-wrap mt-1">
                {log.tags.functions?.slice(0, 3).map((func, idx) => (
                  <motion.span
                    key={idx}
                    animate={{ opacity: 1, scale: 1 }}
                    className="px-1 py-0.5 text-xs bg-green-800 text-green-200 rounded border border-green-600"
                    initial={{ opacity: 0, scale: 0.8 }}
                    transition={{ delay: idx * 0.1 }}
                  >
                    {func}()
                  </motion.span>
                ))}
                {log.tags.actions?.slice(0, 3).map((action, idx) => (
                  <motion.span
                    key={idx}
                    animate={{ opacity: 1, scale: 1 }}
                    className="px-1 py-0.5 text-xs bg-orange-800 text-orange-200 rounded border border-orange-600"
                    initial={{ opacity: 0, scale: 0.8 }}
                    transition={{
                      delay: ((log.tags.functions?.length || 0) + idx) * 0.1,
                    }}
                  >
                    {action}
                  </motion.span>
                ))}
              </div>
            )}
          </div>
        </td>
      </motion.tr>
    );
  },
);

LogRow.displayName = "LogRow";
