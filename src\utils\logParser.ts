// 日志解析工具函数

import type { LogEntry } from "@/types/log";
import {
  QT_LOG_REGEX,
  SIMPLE_TIMESTAMP_REGEX,
  QT_TIMESTAMP_FORMAT_REGEX,
  ACTION_KEYWORDS,
  CHINESE_ACTION_KEYWORDS,
} from "@/constants/log";

/**
 * 解析Qt日志格式的单行日志
 * 格式: YYYYMMDD-HHMMSS.mmm<INFO :THREADID>: <Info> [MODULE][SCOPE][CATEGORY] MESSAGE
 */
export const parseLogLine = (line: string, index: number): LogEntry | null => {
  // 尝试Qt日志格式
  const qtMatch = line.match(QT_LOG_REGEX);
  
  if (qtMatch) {
    const [
      ,
      rawTimestamp,
      level,
      threadId,
      logLevel,
      module,
      scope,
      category,
      message,
    ] = qtMatch;

    return {
      id: index + 1,
      timestamp: formatTimestamp(rawTimestamp),
      rawTimestamp,
      threadId,
      level: (level || logLevel || "INFO").toUpperCase(),
      module: module ? module.trim() : "Unknown",
      scope: scope ? scope.trim() : "Global",
      category: category ? category.trim() : "Unknown",
      message: message ? message.trim() : "",
      autoCategory: categorizeLog(message || "", module || "Unknown", level),
      tags: extractTags(message || "", module || "Unknown"),
    };
  }

  // 兜底：尝试简单的时间戳格式
  const simpleMatch = line.match(SIMPLE_TIMESTAMP_REGEX);
  
  if (simpleMatch) {
    const [, timestamp, level = "INFO", message = ""] = simpleMatch;

    return {
      id: index + 1,
      timestamp: formatTimestamp(timestamp),
      rawTimestamp: timestamp,
      threadId: "main",
      level: level.toUpperCase(),
      module: "Unknown",
      scope: "Unknown",
      category: "Unknown",
      message: message.trim(),
      autoCategory: categorizeLog(message, "Unknown", level),
      tags: extractTags(message, "Unknown"),
    };
  }

  return null;
};

/**
 * 格式化时间戳
 * 将 YYYYMMDD-HHMMSS.mmm 格式转换为更易读的格式
 */
export const formatTimestamp = (timestamp: string): string => {
  if (QT_TIMESTAMP_FORMAT_REGEX.test(timestamp)) {
    const date = timestamp.substring(0, 8);
    const time = timestamp.substring(9);
    const year = date.substring(0, 4);
    const month = date.substring(4, 6);
    const day = date.substring(6, 8);
    const hours = time.substring(0, 2);
    const minutes = time.substring(2, 4);
    const seconds = time.substring(4, 6);
    const ms = time.substring(7, 10);

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${ms}`;
  }

  return timestamp;
};

/**
 * 自动分类日志
 * 根据消息内容和模块名自动分为 Trace、System、UI 三类，无法分类的返回 "-"
 */
export const categorizeLog = (
  message: string,
  module: string,
  level: string,
): "Trace" | "System" | "UI" | "-" => {
  const msg = message.toLowerCase();
  const mod = module.toLowerCase();

  // Trace: 应用生命周期事件
  if (
    msg.includes("start") ||
    msg.includes("stop") ||
    msg.includes("init") ||
    msg.includes("shutdown") ||
    msg.includes("launch") ||
    msg.includes("exit") ||
    msg.includes("启动") ||
    msg.includes("关闭") ||
    msg.includes("初始化")
  ) {
    return "Trace";
  }

  // UI: 用户界面交互
  if (
    mod.includes("ui") ||
    mod.includes("gui") ||
    mod.includes("widget") ||
    msg.includes("click") ||
    msg.includes("button") ||
    msg.includes("window") ||
    msg.includes("dialog") ||
    msg.includes("界面") ||
    msg.includes("窗口") ||
    msg.includes("按钮") ||
    msg.includes("点击")
  ) {
    return "UI";
  }

  // System: 明确的系统相关关键词
  if (
    msg.includes("system") ||
    msg.includes("config") ||
    msg.includes("setting") ||
    msg.includes("version") ||
    msg.includes("path") ||
    msg.includes("file") ||
    msg.includes("directory") ||
    msg.includes("系统") ||
    msg.includes("配置") ||
    msg.includes("设置") ||
    msg.includes("版本") ||
    msg.includes("路径") ||
    msg.includes("文件") ||
    msg.includes("目录") ||
    mod.includes("system") ||
    mod.includes("config") ||
    mod.includes("setting")
  ) {
    return "System";
  }

  // 无法明确分类的返回 "-"
  return "-";
};

/**
 * 提取标签
 * 从日志消息中提取函数名、模块名、动作关键词等标签
 */
export const extractTags = (
  message: string,
  module: string,
): { functions: string[]; modules: string[]; actions: string[] } => {
  const tags = {
    functions: [] as string[],
    modules: [] as string[],
    actions: [] as string[],
  };

  // 提取函数名 (格式如 functionName() 或 ::functionName)
  const functionMatches = message.match(/(\w+)\s*\(|::(\w+)/g);
  
  if (functionMatches) {
    functionMatches.forEach((match) => {
      const func = match.replace(/[()::]/g, "").trim();
      
      if (func && !tags.functions.includes(func)) {
        tags.functions.push(func);
      }
    });
  }

  // 提取模块名
  if (module && module !== "Unknown" && !tags.modules.includes(module)) {
    tags.modules.push(module);
  }

  // 提取动作关键词
  const allActionKeywords = [...ACTION_KEYWORDS, ...CHINESE_ACTION_KEYWORDS];
  
  allActionKeywords.forEach((action) => {
    if (
      message.toLowerCase().includes(action.toLowerCase()) &&
      !tags.actions.includes(action)
    ) {
      tags.actions.push(action);
    }
  });

  return tags;
};

/**
 * 批量解析日志内容
 * 将文本内容按行解析为LogEntry数组
 */
export const parseLogContent = async (
  content: string,
  onProgress?: (progress: number, processedLines: number, totalLines: number) => void,
): Promise<LogEntry[]> => {
  const lines = content.split("\n").filter((line) => line.trim());
  const parsedLogs: LogEntry[] = [];
  const totalLines = lines.length;

  for (let i = 0; i < lines.length; i++) {
    const logEntry = parseLogLine(lines[i], i);

    if (logEntry) {
      parsedLogs.push(logEntry);
    }

    // 每处理一定数量的行就报告进度
    if (i % 1000 === 0 && onProgress) {
      const progress = Math.round((i / totalLines) * 100);
      onProgress(progress, i, totalLines);

      // 让界面有机会更新
      await new Promise((resolve) => setTimeout(resolve, 0));
    }
  }

  // 最终进度报告
  if (onProgress) {
    onProgress(100, totalLines, totalLines);
  }

  return parsedLogs;
};

/**
 * 验证日志文件格式
 * 检查文件内容是否包含可识别的日志格式
 */
export const validateLogFormat = (content: string): boolean => {
  const lines = content.split("\n").filter((line) => line.trim());
  
  // 检查前10行，至少有一行能被解析
  const testLines = lines.slice(0, 10);
  
  for (const line of testLines) {
    if (parseLogLine(line, 0) !== null) {
      return true;
    }
  }

  return false;
};

/**
 * 获取日志内容的基本信息
 * 快速扫描获取总行数、时间范围等基本信息
 */
export const getLogPreview = (content: string) => {
  const lines = content.split("\n").filter((line) => line.trim());
  const totalLines = lines.length;
  
  if (totalLines === 0) {
    return {
      totalLines: 0,
      firstTimestamp: null,
      lastTimestamp: null,
      sampleEntries: [],
    };
  }

  // 解析第一行和最后一行获取时间范围
  const firstEntry = parseLogLine(lines[0], 0);
  const lastEntry = parseLogLine(lines[lines.length - 1], lines.length - 1);
  
  // 获取前几行作为样本
  const sampleEntries = lines
    .slice(0, 5)
    .map((line, index) => parseLogLine(line, index))
    .filter((entry): entry is LogEntry => entry !== null);

  return {
    totalLines,
    firstTimestamp: firstEntry?.timestamp || null,
    lastTimestamp: lastEntry?.timestamp || null,
    sampleEntries,
  };
}; 