// 日志相关常量定义

import type { LogLevel, LogCategory, SearchFieldOption, SearchOperatorOption } from "@/types/log";

// 日志级别列表
export const LOG_LEVELS: LogLevel[] = ["ALL", "ERROR", "WARN", "INFO", "DEBUG", "TRACE", "RAW"];

// 日志分类列表
export const LOG_CATEGORIES: LogCategory[] = ["ALL", "Trace", "System", "UI", "-"];

// 搜索字段选项
export const SEARCH_FIELDS: SearchFieldOption[] = [
  { key: "message", label: "消息内容" },
  { key: "module", label: "模块" },
  { key: "category", label: "分类" },
  { key: "threadId", label: "线程ID" },
  { key: "level", label: "日志级别" },
  { key: "autoCategory", label: "自动分类" },
  { key: "timestamp", label: "时间" },
];

// 文本搜索操作符选项
export const TEXT_SEARCH_OPERATORS: SearchOperatorOption[] = [
  { key: "contains", label: "包含" },
  { key: "not_contains", label: "不包含" },
  { key: "equals", label: "等于" },
  { key: "not_equals", label: "不等于" },
  { key: "starts_with", label: "开始于" },
  { key: "ends_with", label: "结束于" },
];

// 数值搜索操作符选项
export const NUMERIC_SEARCH_OPERATORS: SearchOperatorOption[] = [
  { key: "equals", label: "等于" },
  { key: "not_equals", label: "不等于" },
  { key: "greater_than", label: "大于" },
  { key: "less_than", label: "小于" },
  { key: "greater_equal", label: "大于等于" },
  { key: "less_equal", label: "小于等于" },
];

// 分类搜索操作符选项
export const CATEGORY_SEARCH_OPERATORS: SearchOperatorOption[] = [
  { key: "equals", label: "等于" },
  { key: "not_equals", label: "不等于" },
  { key: "in", label: "包含在" },
  { key: "not_in", label: "不包含在" },
];

// 时间搜索操作符选项
export const TIME_SEARCH_OPERATORS: SearchOperatorOption[] = [
  { key: "equals", label: "等于" },
  { key: "before", label: "早于" },
  { key: "after", label: "晚于" },
  { key: "between", label: "介于" },
];

// 列类型定义
export const COLUMN_TYPES = {
  TEXT: 'text',
  NUMERIC: 'numeric', 
  CATEGORY: 'category',
  TIME: 'time',
} as const;

// 列配置
export const COLUMN_CONFIGS = {
  timestamp: {
    type: COLUMN_TYPES.TIME,
    label: '时间',
    operators: TIME_SEARCH_OPERATORS,
  },
  level: {
    type: COLUMN_TYPES.CATEGORY,
    label: '级别',
    operators: CATEGORY_SEARCH_OPERATORS,
    options: ['ERROR', 'WARN', 'INFO', 'DEBUG', 'TRACE', 'RAW'],
  },
  threadId: {
    type: COLUMN_TYPES.NUMERIC,
    label: '线程',
    operators: NUMERIC_SEARCH_OPERATORS,
  },
  module: {
    type: COLUMN_TYPES.TEXT,
    label: '模块',
    operators: TEXT_SEARCH_OPERATORS,
  },
  autoCategory: {
    type: COLUMN_TYPES.CATEGORY,
    label: '分类',
    operators: CATEGORY_SEARCH_OPERATORS,
    options: ['Trace', 'System', 'UI', '-'],
  },
  message: {
    type: COLUMN_TYPES.TEXT,
    label: '消息',
    operators: TEXT_SEARCH_OPERATORS,
  },
} as const;

// 搜索操作符选项（兼容性）
export const SEARCH_OPERATORS: SearchOperatorOption[] = TEXT_SEARCH_OPERATORS;

// 文件大小限制（字节）
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// 支持的文件扩展名
export const SUPPORTED_EXTENSIONS = [".log", ".txt"];

// 表格显示的最大行数
export const MAX_TABLE_ROWS = 50;

// 批量处理的行数
export const BATCH_PROCESS_SIZE = 1000;

// 动作关键词（英文）
export const ACTION_KEYWORDS = [
  "start", "stop", "init", "load", "save", "update", 
  "delete", "create", "send", "receive", "connect", "disconnect"
];

// 动作关键词（中文）
export const CHINESE_ACTION_KEYWORDS = [
  "启动", "停止", "初始化", "加载", "保存", "更新", 
  "删除", "创建", "发送", "接收", "连接", "断开"
];
