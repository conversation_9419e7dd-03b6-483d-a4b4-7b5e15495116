// 日志相关常量定义

import type { LogLevel, LogCategory, SearchFieldOption, SearchOperatorOption } from "@/types/log";

// 日志级别列表
export const LOG_LEVELS: LogLevel[] = ["ALL", "ERROR", "WARN", "INFO", "DEBUG", "TRACE"];

// 日志分类列表
export const LOG_CATEGORIES: LogCategory[] = ["ALL", "Trace", "System", "UI", "-"];

// 搜索字段选项
export const SEARCH_FIELDS: SearchFieldOption[] = [
  { key: "message", label: "消息内容" },
  { key: "module", label: "模块" },
  { key: "category", label: "分类" },
  { key: "threadId", label: "线程ID" },
  { key: "level", label: "日志级别" },
  { key: "autoCategory", label: "自动分类" },
];

// 搜索操作符选项
export const SEARCH_OPERATORS: SearchOperatorOption[] = [
  { key: "contains", label: "包含" },
  { key: "not_contains", label: "不包含" },
  { key: "equals", label: "等于" },
  { key: "not_equals", label: "不等于" },
];

// 文件大小限制（字节）
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

// 支持的文件扩展名
export const SUPPORTED_EXTENSIONS = [".log", ".txt"];

// 表格显示的最大行数
export const MAX_TABLE_ROWS = 50;

// 批量处理的行数
export const BATCH_PROCESS_SIZE = 1000;

// 动作关键词（英文）
export const ACTION_KEYWORDS = [
  "start", "stop", "init", "load", "save", "update", 
  "delete", "create", "send", "receive", "connect", "disconnect"
];

// 动作关键词（中文）
export const CHINESE_ACTION_KEYWORDS = [
  "启动", "停止", "初始化", "加载", "保存", "更新", 
  "删除", "创建", "发送", "接收", "连接", "断开"
];

// Qt日志格式正则表达式
export const QT_LOG_REGEX = /^(\d{8}-\d{6}\.\d{3})<(\w+)\s*:(\w+)>:\s*<(\w+)>\s*\[([^\]]*)\]\[([^\]]*)\]\[([^\]]*)\]\s*(.*)$/;

// 简单时间戳格式正则表达式（兜底）
export const SIMPLE_TIMESTAMP_REGEX = /^(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}(?:\.\d{3})?)\s*(\w+)?\s*(.*)$/;

// Qt时间戳格式检查正则
export const QT_TIMESTAMP_FORMAT_REGEX = /^\d{8}-\d{6}\.\d{3}$/; 