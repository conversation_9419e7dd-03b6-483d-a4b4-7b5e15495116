# 项目结构规则

## 概述
本项目是基于 React + TypeScript + Vite + HeroUI + TailwindCSS 构建的现代化前端应用。

## 核心技术栈
- **框架**: React 18+ with TypeScript
- **构建工具**: Vite
- **UI 库**: HeroUI (NextUI), doc: https://hero-ui.com/docs/getting-started/installation
- **样式**: TailwindCSS + PostCSS
- **部署**: Vercel

## 目录结构规范

### 根目录文件
```
├── index.html              # 入口 HTML 文件，仅在需要修改元数据时修改
├── package.json            # 依赖管理，添加新依赖时更新
├── vite.config.ts          # Vite 配置，插件和构建相关设置
├── tsconfig.json           # TypeScript 主配置
├── tsconfig.node.json      # Node.js TypeScript 配置
├── tailwind.config.js      # TailwindCSS 配置，主题和扩展设置
├── postcss.config.js       # PostCSS 配置
├── eslint.config.mjs       # ESLint 配置
└── vercel.json             # Vercel 部署配置
```

### 源码目录 (src/)

#### 主要入口文件
- **main.tsx**: 应用入口点，挂载根组件
- **App.tsx**: 根组件，路由配置和全局布局
- **provider.tsx**: 全局状态提供者（主题、国际化等）
- **vite-env.d.ts**: Vite 环境类型声明

#### 组件目录 (src/components/)
```
components/
├── icons.tsx           # 所有图标组件的集合
├── navbar.tsx          # 导航栏组件
├── theme-switch.tsx    # 主题切换组件
└── primitives.ts       # 基础原子组件和 UI 原语
```

**组件规则**:
- 所有可复用的 UI 组件放在此目录
- 文件名使用 kebab-case
- 组件名使用 PascalCase
- 图标统一在 `icons.tsx` 中管理
- 基础组件和 UI 原语在 `primitives.ts` 中定义

#### 页面目录 (src/pages/)
```
pages/
└── index.tsx         # 首页
```

**页面规则**:
- 每个路由对应一个页面文件
- 页面组件负责该路由的主要业务逻辑
- 文件名与路由路径对应
- 页面组件应该使用布局组件包装

#### 布局目录 (src/layouts/)
```
layouts/
└── default.tsx         # 默认布局组件
```

**布局规则**:
- 所有页面布局模板放在此目录
- 包含导航、页脚等公共元素
- 页面组件通过布局组件包装

#### 配置目录 (src/config/)
```
config/
└── site.ts             # 站点全局配置
```

**配置规则**:
- 所有应用级配置放在此目录
- 包括站点信息、API 端点、常量等
- 使用 TypeScript 确保类型安全

#### 样式目录 (src/styles/)
```
styles/
└── globals.css         # 全局样式
```

**样式规则**:
- 全局样式和 CSS 变量定义
- TailwindCSS 基础样式导入
- 自定义全局样式覆盖

#### 类型目录 (src/types/)
```
types/
└── index.ts            # 全局类型定义
```

**类型规则**:
- 所有全局 TypeScript 类型定义
- 接口、类型别名、枚举等
- 按功能模块组织类型

#### 静态资源目录 (public/)
```
public/
└── vite.svg            # 静态资源文件
```

**静态资源规则**:
- 图片、字体、图标等静态文件
- 不会被 Vite 处理的资源
- 通过绝对路径引用

## 代码组织规则

### 文件命名约定
- **组件文件**: kebab-case (如 `theme-switch.tsx`)
- **页面文件**: kebab-case (如 `about.tsx`) 
- **配置文件**: kebab-case (如 `site.ts`)
- **类型文件**: kebab-case (如 `index.ts`)

### 导入导出规则
- 优先使用命名导出而非默认导出
- 组件使用默认导出
- 工具函数、常量使用命名导出
- 使用绝对路径导入 (通过 Vite alias 配置)

### 组件开发规则
- 使用函数式组件 + Hooks
- 组件文件结构：导入 → 类型定义 → 组件实现 → 导出
- 使用 HeroUI 组件作为基础 UI
- 样式优先使用 TailwindCSS 类名

### 新功能开发指导

#### 添加新页面
1. 在 `src/pages/` 创建页面组件
2. 在 `App.tsx` 中添加路由配置
3. 更新导航链接（如需要）

#### 添加新组件
1. 在 `src/components/` 创建组件文件
2. 导出组件供其他模块使用
3. 如果是图标，添加到 `icons.tsx`

#### 添加新配置
1. 在 `src/config/` 添加配置文件
2. 确保类型安全和导出清晰

#### 添加新类型
1. 在 `src/types/` 目录添加类型定义
2. 从 `index.ts` 统一导出

## 代码质量要求

### TypeScript 规范
- 严格模式，所有类型必须明确定义
- 避免使用 `any` 类型
- 组件 Props 必须定义接口

### 代码风格
- 使用 ESLint 和 Prettier 保持代码风格一致
- 遵循 React Hooks 最佳实践
- 组件内部逻辑清晰，职责单一

### 性能考虑
- 适当使用 React.memo 优化组件渲染
- 大型列表使用虚拟滚动
- 图片使用适当的格式和大小

## 特别注意事项

1. **不要修改已有的正确功能**: 在添加新功能时，保持现有工作代码不变
2. **中文注释**: 所有注释使用中文，确保 UTF-8 编码
3. **避免中文乱码**: 生成包含中文的代码时检查编码正确性
4. **渐进式增强**: 在现有函数基础上扩展，不要完全重写
5. **保持一致性**: 新代码应与现有代码风格保持一致

## 部署和构建

- **开发环境**: `npm run dev` 启动本地开发服务器
- **构建**: `npm run build` 构建生产版本
- **预览**: `npm run preview` 预览构建结果
- **部署**: 通过 Vercel 自动部署，配置见 `vercel.json`

这个项目结构旨在提供清晰的代码组织和良好的开发体验，请在后续开发中严格遵循这些规则。


# 项目结构规则（project-structure.mdc）

