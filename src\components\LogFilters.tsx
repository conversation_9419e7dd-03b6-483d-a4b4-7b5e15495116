// 日志筛选器组件

import React from "react";
import { But<PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import { Switch } from "@heroui/switch";
import {
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
} from "@heroui/dropdown";
import { motion, AnimatePresence } from "framer-motion";
import type { LogLevel, LogCategory, AdvancedSearchConfig, SearchCondition } from "@/types/log";
import { LOG_LEVELS, LOG_CATEGORIES, SEARCH_FIELDS, SEARCH_OPERATORS } from "@/constants/log";
import { sidebarVariants, containerVariants, itemVariants } from "@/animations/variants";

interface LogFiltersProps {
  selectedLevel: LogLevel | "ALL";
  selectedCategory: LogCategory | "ALL";
  onLevelChange: (level: LogLevel | "ALL") => void;
  onCategoryChange: (category: LogCategory | "ALL") => void;
  stats: {
    total: number;
    error: number;
    warn: number;
    trace: number;
    system: number;
    ui: number;
  };
  // 高级搜索相关属性
  advancedSearch: AdvancedSearchConfig;
  onUpdateCondition: (id: string, updates: Partial<SearchCondition>) => void;
  onRemoveCondition: (id: string) => void;
  onAddCondition: () => void;
  onSetTimeRange: (timeRange: Partial<AdvancedSearchConfig["timeRange"]>) => void;
  onApplySearch: () => void;
  onClearAdvancedSearch: () => void;
}

/**
 * 日志筛选器组件
 * 提供基础筛选和高级搜索功能
 */
export const LogFilters: React.FC<LogFiltersProps> = ({
  selectedLevel,
  selectedCategory,
  onLevelChange,
  onCategoryChange,
  stats,
  advancedSearch,
  onUpdateCondition,
  onRemoveCondition,
  onAddCondition,
  onSetTimeRange,
  onApplySearch,
  onClearAdvancedSearch,
}) => {
  const [isAdvancedExpanded, setIsAdvancedExpanded] = React.useState(false);
  
  const hasValidConditions = advancedSearch.conditions.some((c) => c.value.trim());
  const canApply = hasValidConditions || advancedSearch.timeRange.enabled;
  const hasActiveSearch = canApply && (advancedSearch.conditions.length > 0 || advancedSearch.timeRange.enabled);

  return (
    <motion.div
      className="w-80 bg-gray-800 border-r border-gray-700 p-4 overflow-y-auto scrollbar-thin"
      variants={sidebarVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div className="space-y-6" variants={containerVariants}>
        <motion.div variants={itemVariants}>
          <h3 className="text-lg font-semibold text-blue-400 mb-4">筛选器</h3>
        </motion.div>

        {/* 基础筛选 */}
        <motion.div className="space-y-4" variants={itemVariants}>
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              日志级别
            </label>
            <Dropdown>
              <DropdownTrigger>
                <Button
                  className="w-full justify-start bg-gray-700 border-gray-600 text-gray-100"
                  variant="bordered"
                >
                  日志级别: {selectedLevel}
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                className="bg-gray-700 border-gray-600"
                onAction={(key) => onLevelChange(key as LogLevel | "ALL")}
              >
                <DropdownItem key="ALL" className="text-gray-300 hover:bg-gray-600">
                  ALL
                </DropdownItem>
                {LOG_LEVELS.map((level) => (
                  <DropdownItem
                    key={level}
                    className="text-gray-300 hover:bg-gray-600"
                  >
                    {level}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </Dropdown>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              日志分类
            </label>
            <Dropdown>
              <DropdownTrigger>
                <Button
                  className="w-full justify-start bg-gray-700 border-gray-600 text-gray-100"
                  variant="bordered"
                >
                  日志分类: {selectedCategory}
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                className="bg-gray-700 border-gray-600"
                onAction={(key) => onCategoryChange(key as LogCategory | "ALL")}
              >
                <DropdownItem key="ALL" className="text-gray-300 hover:bg-gray-600">
                  ALL
                </DropdownItem>
                {LOG_CATEGORIES.map((category) => (
                  <DropdownItem
                    key={category}
                    className="text-gray-300 hover:bg-gray-600"
                  >
                    {category}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </Dropdown>
          </div>
        </motion.div>

        {/* 高级搜索区域 */}
        <motion.div variants={itemVariants} className="border-t border-gray-700 pt-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-md font-medium text-blue-400">高级搜索</h4>
            <div className="flex gap-2">
              {hasActiveSearch && (
                <Button
                  className="bg-red-700 text-red-100 hover:bg-red-600"
                  size="sm"
                  onPress={onClearAdvancedSearch}
                >
                  清除
                </Button>
              )}
              <Button
                className={`${
                  isAdvancedExpanded 
                    ? "bg-blue-700 text-blue-100" 
                    : "border-gray-600 text-gray-300"
                }`}
                size="sm"
                variant={isAdvancedExpanded ? "solid" : "bordered"}
                onPress={() => setIsAdvancedExpanded(!isAdvancedExpanded)}
              >
                {isAdvancedExpanded ? "收起" : "展开"}
              </Button>
            </div>
          </div>

          <AnimatePresence>
            {isAdvancedExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-4 overflow-hidden"
              >
                {/* 时间范围 */}
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Switch
                      isSelected={advancedSearch.timeRange.enabled}
                      size="sm"
                      onValueChange={(enabled) => onSetTimeRange({ enabled })}
                    />
                    <span className="text-sm font-medium text-gray-300">
                      时间范围
                    </span>
                  </div>
                  {advancedSearch.timeRange.enabled && (
                    <div className="space-y-2 pl-6">
                      <Input
                        classNames={{
                          input: "bg-gray-700 text-gray-100",
                          inputWrapper: "bg-gray-700 border-gray-600",
                        }}
                        label="开始时间"
                        size="sm"
                        type="datetime-local"
                        value={advancedSearch.timeRange.start}
                        onValueChange={(value) => onSetTimeRange({ start: value })}
                      />
                      <Input
                        classNames={{
                          input: "bg-gray-700 text-gray-100",
                          inputWrapper: "bg-gray-700 border-gray-600",
                        }}
                        label="结束时间"
                        size="sm"
                        type="datetime-local"
                        value={advancedSearch.timeRange.end}
                        onValueChange={(value) => onSetTimeRange({ end: value })}
                      />
                    </div>
                  )}
                </div>

                {/* 搜索条件 */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-300">
                      搜索条件
                    </span>
                    <Button
                      className="bg-green-700 text-green-100 hover:bg-green-600"
                      size="sm"
                      onPress={onAddCondition}
                    >
                      ➕
                    </Button>
                  </div>

                  {advancedSearch.conditions.length === 0 ? (
                    <div className="text-center py-4 text-gray-400 text-sm">
                      <p>暂无搜索条件</p>
                    </div>
                  ) : (
                    <div className="space-y-3 max-h-80 overflow-y-auto scrollbar-thin">
                      <AnimatePresence>
                        {advancedSearch.conditions.map((condition, index) => (
                          <motion.div
                            key={condition.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
                            className="space-y-2 p-3 border border-gray-600 rounded-lg bg-gray-750"
                          >
                            {index > 0 && (
                              <div className="text-xs text-gray-400 mb-2">
                                AND 关系
                              </div>
                            )}

                            {/* 搜索字段 */}
                            <div>
                              <span className="text-xs text-gray-400 mb-1 block">
                                搜索字段
                              </span>
                              <Dropdown>
                                <DropdownTrigger>
                                  <Button
                                    className="w-full justify-start bg-gray-700 border-gray-600 text-gray-100"
                                    size="sm"
                                    variant="bordered"
                                  >
                                    {SEARCH_FIELDS.find(f => f.key === condition.field)?.label || condition.field}
                                  </Button>
                                </DropdownTrigger>
                                <DropdownMenu
                                  className="bg-gray-700 border-gray-600"
                                  onAction={(key) => {
                                    onUpdateCondition(condition.id, { field: key as string });
                                  }}
                                >
                                  {SEARCH_FIELDS.map((field) => (
                                    <DropdownItem
                                      key={field.key}
                                      className="text-gray-300 hover:bg-gray-600"
                                    >
                                      {field.label}
                                    </DropdownItem>
                                  ))}
                                </DropdownMenu>
                              </Dropdown>
                            </div>

                            {/* 操作符 */}
                            <div>
                              <span className="text-xs text-gray-400 mb-1 block">
                                操作符
                              </span>
                              <Dropdown>
                                <DropdownTrigger>
                                  <Button
                                    className="w-full justify-start bg-gray-700 border-gray-600 text-gray-100"
                                    size="sm"
                                    variant="bordered"
                                  >
                                    {SEARCH_OPERATORS.find(op => op.key === condition.operator)?.label || condition.operator}
                                  </Button>
                                </DropdownTrigger>
                                <DropdownMenu
                                  className="bg-gray-700 border-gray-600"
                                  onAction={(key) => {
                                    onUpdateCondition(condition.id, { operator: key as string });
                                  }}
                                >
                                  {SEARCH_OPERATORS.map((op) => (
                                    <DropdownItem
                                      key={op.key}
                                      className="text-gray-300 hover:bg-gray-600"
                                    >
                                      {op.label}
                                    </DropdownItem>
                                  ))}
                                </DropdownMenu>
                              </Dropdown>
                            </div>

                            {/* 搜索值 */}
                            <div>
                              <Input
                                classNames={{
                                  input: "bg-gray-700 text-gray-100",
                                  inputWrapper: "bg-gray-700 border-gray-600",
                                }}
                                label="搜索值"
                                size="sm"
                                value={condition.value}
                                onValueChange={(value) =>
                                  onUpdateCondition(condition.id, { value })
                                }
                              />
                            </div>

                            {/* 底部控制 */}
                            <div className="flex items-center justify-between">
                              <Switch
                                isSelected={condition.caseSensitive}
                                size="sm"
                                onValueChange={(caseSensitive) =>
                                  onUpdateCondition(condition.id, { caseSensitive })
                                }
                              >
                                <span className="text-xs text-gray-300">
                                  区分大小写
                                </span>
                              </Switch>
                              <Button
                                isIconOnly
                                className="bg-red-700 text-red-100 hover:bg-red-600"
                                size="sm"
                                onPress={() => onRemoveCondition(condition.id)}
                              >
                                🗑️
                              </Button>
                            </div>
                          </motion.div>
                        ))}
                      </AnimatePresence>
                    </div>
                  )}
                </div>

                {/* 应用按钮 */}
                <div className="pt-2">
                  <Button
                    className="w-full bg-blue-700 text-blue-100 hover:bg-blue-600"
                    isDisabled={!canApply}
                    onPress={onApplySearch}
                  >
                    应用高级搜索
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* 统计信息 */}
        <motion.div variants={itemVariants}>
          <h4 className="text-md font-medium text-green-400 mb-3">统计信息</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-300">总数</span>
              <span className="text-white font-mono">{stats.total}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-red-400">错误</span>
              <span className="text-red-300 font-mono">{stats.error}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-yellow-400">警告</span>
              <span className="text-yellow-300 font-mono">{stats.warn}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-blue-400">Trace</span>
              <span className="text-blue-300 font-mono">{stats.trace}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-purple-400">System</span>
              <span className="text-purple-300 font-mono">{stats.system}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-400">UI</span>
              <span className="text-green-300 font-mono">{stats.ui}</span>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}; 