// 列搜索弹出框组件

import React, { useState } from "react";
import { Popover, PopoverTrigger, PopoverContent } from "@heroui/popover";
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import { Select, SelectItem } from "@heroui/select";
import { Switch } from "@heroui/switch";
import { Divider } from "@heroui/divider";
import { motion } from "framer-motion";
import { SearchIcon, FilterIcon, XIcon } from "lucide-react";

import type { SearchCondition } from "@/types/log";
import { SEARCH_OPERATORS } from "@/constants/log";

interface ColumnSearchPopoverProps {
  columnKey: string;
  columnLabel: string;
  onAddCondition: (condition: Omit<SearchCondition, 'id'>) => void;
  children: React.ReactNode;
}

/**
 * 列搜索弹出框组件
 * 为表格列提供高级搜索功能
 */
export const ColumnSearchPopover: React.FC<ColumnSearchPopoverProps> = ({
  columnK<PERSON>,
  columnLabel,
  onAddCondition,
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [operator, setOperator] = useState("contains");
  const [caseSensitive, setCaseSensitive] = useState(false);

  const handleApplySearch = () => {
    if (searchValue.trim()) {
      onAddCondition({
        field: columnKey,
        operator,
        value: searchValue.trim(),
        caseSensitive,
      });
      
      // 重置表单
      setSearchValue("");
      setOperator("contains");
      setCaseSensitive(false);
      setIsOpen(false);
    }
  };

  const handleClear = () => {
    setSearchValue("");
    setOperator("contains");
    setCaseSensitive(false);
  };

  return (
    <Popover 
      isOpen={isOpen} 
      onOpenChange={setIsOpen}
      placement="bottom-start"
      showArrow
      classNames={{
        content: "bg-gray-800 border border-gray-600 shadow-xl",
      }}
    >
      <PopoverTrigger>
        <div className="cursor-pointer hover:bg-gray-700 transition-colors duration-200 rounded px-1 py-1">
          {children}
        </div>
      </PopoverTrigger>
      
      <PopoverContent className="w-80 p-0">
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4"
        >
          {/* 标题 */}
          <div className="flex items-center gap-2 mb-4">
            <FilterIcon className="w-4 h-4 text-blue-400" />
            <h4 className="text-sm font-medium text-gray-100">
              高级搜索 - {columnLabel}
            </h4>
          </div>

          <div className="space-y-4">
            {/* 搜索操作符 */}
            <div>
              <label className="block text-xs text-gray-300 mb-2">
                搜索条件
              </label>
              <Select
                selectedKeys={[operator]}
                onSelectionChange={(keys) => setOperator(Array.from(keys)[0] as string)}
                classNames={{
                  trigger: "bg-gray-700 border-gray-600 text-gray-100",
                  content: "bg-gray-700 border-gray-600",
                  item: "text-gray-100 data-[hover=true]:bg-gray-600",
                }}
                size="sm"
              >
                {SEARCH_OPERATORS.map((op) => (
                  <SelectItem key={op.key} value={op.key}>
                    {op.label}
                  </SelectItem>
                ))}
              </Select>
            </div>

            {/* 搜索值 */}
            <div>
              <label className="block text-xs text-gray-300 mb-2">
                搜索内容
              </label>
              <Input
                value={searchValue}
                onValueChange={setSearchValue}
                placeholder={`输入要搜索的${columnLabel}...`}
                classNames={{
                  input: "bg-gray-700 text-gray-100",
                  inputWrapper: "bg-gray-700 border-gray-600",
                }}
                size="sm"
                startContent={<SearchIcon className="w-4 h-4 text-gray-400" />}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleApplySearch();
                  }
                }}
              />
            </div>

            {/* 大小写敏感 */}
            <div className="flex items-center justify-between">
              <label className="text-xs text-gray-300">
                区分大小写
              </label>
              <Switch
                isSelected={caseSensitive}
                onValueChange={setCaseSensitive}
                size="sm"
                classNames={{
                  wrapper: "bg-gray-600",
                  thumb: "bg-gray-200",
                }}
              />
            </div>

            <Divider className="bg-gray-600" />

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <Button
                size="sm"
                className="flex-1 bg-blue-600 text-blue-100 hover:bg-blue-500"
                onPress={handleApplySearch}
                isDisabled={!searchValue.trim()}
              >
                添加条件
              </Button>
              <Button
                size="sm"
                variant="bordered"
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
                onPress={handleClear}
              >
                <XIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </motion.div>
      </PopoverContent>
    </Popover>
  );
};
