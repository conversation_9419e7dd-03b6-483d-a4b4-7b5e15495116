// 列搜索弹出框组件

import React, { useState, useMemo } from "react";
import { Popover, PopoverTrigger, PopoverContent } from "@heroui/popover";
import { But<PERSON> } from "@heroui/button";
import { Input } from "@heroui/input";
import { Select, SelectItem } from "@heroui/select";
import { Switch } from "@heroui/switch";
import { Divider } from "@heroui/divider";
import { CheckboxGroup, Checkbox } from "@heroui/checkbox";
import { DatePicker } from "@heroui/date-picker";
import { motion } from "framer-motion";
import { SearchIcon, FilterIcon, XIcon, CalendarIcon } from "lucide-react";

import type { SearchCondition, LogEntry } from "@/types/log";
import { COLUMN_CONFIGS, COLUMN_TYPES } from "@/constants/log";

interface ColumnSearchPopoverProps {
  columnKey: string;
  columnLabel: string;
  onAddCondition: (condition: Omit<SearchCondition, 'id'>) => void;
  logs: LogEntry[]; // 用于获取可用的选项值
  activeFilters?: SearchCondition[]; // 当前活跃的过滤器
  children: React.ReactNode;
}

/**
 * 列搜索弹出框组件
 * 为表格列提供高级搜索功能
 */
export const ColumnSearchPopover: React.FC<ColumnSearchPopoverProps> = ({
  columnKey,
  columnLabel,
  onAddCondition,
  logs,
  activeFilters = [],
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [operator, setOperator] = useState("");
  const [caseSensitive, setCaseSensitive] = useState(false);
  const [selectedValues, setSelectedValues] = useState<string[]>([]);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  // 获取列配置
  const columnConfig = COLUMN_CONFIGS[columnKey as keyof typeof COLUMN_CONFIGS];

  // 设置默认操作符
  React.useEffect(() => {
    if (columnConfig && !operator) {
      setOperator(columnConfig.operators[0].key);
    }
  }, [columnConfig, operator]);

  // 获取该列的唯一值（用于分类搜索）
  const uniqueValues = useMemo(() => {
    if (columnConfig?.type !== COLUMN_TYPES.CATEGORY) return [];

    if (columnConfig.options) {
      return columnConfig.options;
    }

    const values = new Set<string>();
    logs.forEach(log => {
      const value = log[columnKey as keyof LogEntry] as string;
      if (value && value.trim()) {
        values.add(value);
      }
    });
    return Array.from(values).sort();
  }, [logs, columnKey, columnConfig]);

  // 检查是否有活跃的过滤器
  const hasActiveFilter = activeFilters.some(filter => filter.field === columnKey);

  const handleApplySearch = () => {
    let condition: Omit<SearchCondition, 'id'>;

    switch (columnConfig?.type) {
      case COLUMN_TYPES.TEXT:
      case COLUMN_TYPES.NUMERIC:
        if (!searchValue.trim()) return;
        condition = {
          field: columnKey,
          operator,
          value: searchValue.trim(),
          caseSensitive,
        };
        break;

      case COLUMN_TYPES.CATEGORY:
        if (selectedValues.length === 0) return;
        condition = {
          field: columnKey,
          operator,
          value: selectedValues.join(','),
          caseSensitive: false,
        };
        break;

      case COLUMN_TYPES.TIME:
        if (operator === 'between') {
          if (!startDate || !endDate) return;
          condition = {
            field: columnKey,
            operator,
            value: `${startDate},${endDate}`,
            caseSensitive: false,
          };
        } else {
          if (!startDate) return;
          condition = {
            field: columnKey,
            operator,
            value: startDate,
            caseSensitive: false,
          };
        }
        break;

      default:
        return;
    }

    onAddCondition(condition);
    handleClear();
    setIsOpen(false);
  };

  const handleClear = () => {
    setSearchValue("");
    setSelectedValues([]);
    setStartDate("");
    setEndDate("");
    setCaseSensitive(false);
    if (columnConfig) {
      setOperator(columnConfig.operators[0].key);
    }
  };

  // 渲染搜索输入组件
  const renderSearchInput = () => {
    if (!columnConfig) return null;

    switch (columnConfig.type) {
      case COLUMN_TYPES.TEXT:
      case COLUMN_TYPES.NUMERIC:
        return (
          <>
            <div>
              <label className="block text-xs text-gray-300 mb-2">
                搜索内容
              </label>
              <Input
                value={searchValue}
                onValueChange={setSearchValue}
                placeholder={`输入要搜索的${columnLabel}...`}
                type={columnConfig.type === COLUMN_TYPES.NUMERIC ? "number" : "text"}
                classNames={{
                  input: "bg-gray-700 text-gray-100",
                  inputWrapper: "bg-gray-700 border-gray-600",
                }}
                size="sm"
                startContent={<SearchIcon className="w-4 h-4 text-gray-400" />}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleApplySearch();
                  }
                }}
              />
            </div>

            {columnConfig.type === COLUMN_TYPES.TEXT && (
              <div className="flex items-center justify-between">
                <label className="text-xs text-gray-300">
                  区分大小写
                </label>
                <Switch
                  isSelected={caseSensitive}
                  onValueChange={setCaseSensitive}
                  size="sm"
                  classNames={{
                    wrapper: "bg-gray-600",
                    thumb: "bg-gray-200",
                  }}
                />
              </div>
            )}
          </>
        );

      case COLUMN_TYPES.CATEGORY:
        return (
          <div>
            <label className="block text-xs text-gray-300 mb-2">
              选择{columnLabel}
            </label>
            <CheckboxGroup
              value={selectedValues}
              onValueChange={setSelectedValues}
              classNames={{
                wrapper: "gap-2",
              }}
            >
              {uniqueValues.map((value) => (
                <Checkbox
                  key={value}
                  value={value}
                  classNames={{
                    base: "text-gray-300 text-sm",
                    wrapper: "border-gray-600",
                  }}
                >
                  {value}
                </Checkbox>
              ))}
            </CheckboxGroup>
          </div>
        );

      case COLUMN_TYPES.TIME:
        return (
          <div className="space-y-3">
            {operator === 'between' ? (
              <>
                <div>
                  <label className="block text-xs text-gray-300 mb-2">
                    开始时间
                  </label>
                  <Input
                    type="datetime-local"
                    value={startDate}
                    onValueChange={setStartDate}
                    classNames={{
                      input: "bg-gray-700 text-gray-100",
                      inputWrapper: "bg-gray-700 border-gray-600",
                    }}
                    size="sm"
                    startContent={<CalendarIcon className="w-4 h-4 text-gray-400" />}
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-300 mb-2">
                    结束时间
                  </label>
                  <Input
                    type="datetime-local"
                    value={endDate}
                    onValueChange={setEndDate}
                    classNames={{
                      input: "bg-gray-700 text-gray-100",
                      inputWrapper: "bg-gray-700 border-gray-600",
                    }}
                    size="sm"
                    startContent={<CalendarIcon className="w-4 h-4 text-gray-400" />}
                  />
                </div>
              </>
            ) : (
              <div>
                <label className="block text-xs text-gray-300 mb-2">
                  选择时间
                </label>
                <Input
                  type="datetime-local"
                  value={startDate}
                  onValueChange={setStartDate}
                  classNames={{
                    input: "bg-gray-700 text-gray-100",
                    inputWrapper: "bg-gray-700 border-gray-600",
                  }}
                  size="sm"
                  startContent={<CalendarIcon className="w-4 h-4 text-gray-400" />}
                />
              </div>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  // 检查是否可以应用搜索
  const canApply = () => {
    if (!columnConfig) return false;

    switch (columnConfig.type) {
      case COLUMN_TYPES.TEXT:
      case COLUMN_TYPES.NUMERIC:
        return searchValue.trim().length > 0;
      case COLUMN_TYPES.CATEGORY:
        return selectedValues.length > 0;
      case COLUMN_TYPES.TIME:
        return operator === 'between' ? (startDate && endDate) : startDate;
      default:
        return false;
    }
  };

  return (
    <Popover
      isOpen={isOpen}
      onOpenChange={setIsOpen}
      placement="bottom-start"
      showArrow
      classNames={{
        content: "bg-gray-800 border border-gray-600 shadow-xl",
      }}
    >
      <PopoverTrigger>
        <div className={`cursor-pointer hover:bg-gray-700 transition-colors duration-200 rounded px-1 py-1 ${hasActiveFilter ? 'bg-blue-900/30' : ''}`}>
          {children}
        </div>
      </PopoverTrigger>

      <PopoverContent className="w-80 p-0">
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-4"
        >
          {/* 标题 */}
          <div className="flex items-center gap-2 mb-4">
            <FilterIcon className={`w-4 h-4 ${hasActiveFilter ? 'text-blue-400' : 'text-gray-400'}`} />
            <h4 className="text-sm font-medium text-gray-100">
              高级搜索 - {columnLabel}
            </h4>
            {hasActiveFilter && (
              <span className="text-xs bg-blue-600 text-blue-100 px-2 py-0.5 rounded">
                已激活
              </span>
            )}
          </div>

          <div className="space-y-4">
            {/* 搜索操作符 */}
            {columnConfig && (
              <div>
                <label className="block text-xs text-gray-300 mb-2">
                  搜索条件
                </label>
                <Select
                  selectedKeys={[operator]}
                  onSelectionChange={(keys) => setOperator(Array.from(keys)[0] as string)}
                  classNames={{
                    trigger: "bg-gray-700 border-gray-600 text-gray-100",
                    content: "bg-gray-700 border-gray-600",
                    item: "text-gray-100 data-[hover=true]:bg-gray-600",
                  }}
                  size="sm"
                >
                  {columnConfig.operators.map((op) => (
                    <SelectItem key={op.key} value={op.key}>
                      {op.label}
                    </SelectItem>
                  ))}
                </Select>
              </div>
            )}

            {/* 搜索输入 */}
            {renderSearchInput()}

            <Divider className="bg-gray-600" />

            {/* 操作按钮 */}
            <div className="flex gap-2">
              <Button
                size="sm"
                className="flex-1 bg-blue-600 text-blue-100 hover:bg-blue-500"
                onPress={handleApplySearch}
                isDisabled={!canApply()}
              >
                应用
              </Button>
              <Button
                size="sm"
                variant="bordered"
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
                onPress={handleClear}
              >
                清除
              </Button>
            </div>
          </div>
        </motion.div>
      </PopoverContent>
    </Popover>
  );
};
