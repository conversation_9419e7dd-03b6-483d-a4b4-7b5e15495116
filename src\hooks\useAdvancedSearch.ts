// 高级搜索Hook

import { useState, useCallback, useMemo } from "react";
import type { SearchCondition, AdvancedSearchConfig, LogEntry } from "@/types/log";

interface UseAdvancedSearchReturn {
  // 搜索配置状态
  advancedSearch: AdvancedSearchConfig;
  isAdvancedSearchActive: boolean;
  
  // 搜索条件管理
  addSearchCondition: () => void;
  removeSearchCondition: (id: string) => void;
  updateSearchCondition: (id: string, updates: Partial<SearchCondition>) => void;
  
  // 搜索配置管理
  setTimeRange: (timeRange: Partial<AdvancedSearchConfig["timeRange"]>) => void;
  
  // 搜索操作
  applyAdvancedSearch: () => void;
  resetAdvancedSearch: () => void;
  
  // 过滤逻辑
  applyAdvancedFilter: (logs: LogEntry[]) => LogEntry[];
}

/**
 * 高级搜索Hook
 * 提供高级搜索的完整功能
 */
export const useAdvancedSearch = (): UseAdvancedSearchReturn => {
  // 高级搜索配置状态
  const [advancedSearch, setAdvancedSearch] = useState<AdvancedSearchConfig>({
    conditions: [],
    logicOperator: "AND",
    timeRange: {
      enabled: false,
      start: "",
      end: "",
    },
  });

  // 高级搜索激活状态
  const [isAdvancedSearchActive, setIsAdvancedSearchActive] = useState(false);

  /**
   * 生成唯一ID
   */
  const generateId = useCallback(() => {
    return Math.random().toString(36).substr(2, 9);
  }, []);

  /**
   * 添加搜索条件
   */
  const addSearchCondition = useCallback(() => {
    const newCondition: SearchCondition = {
      id: generateId(),
      field: "message",
      operator: "contains",
      value: "",
      caseSensitive: false,
    };

    setAdvancedSearch((prev) => ({
      ...prev,
      conditions: [...prev.conditions, newCondition],
    }));
  }, [generateId]);

  /**
   * 删除搜索条件
   */
  const removeSearchCondition = useCallback((id: string) => {
    setAdvancedSearch((prev) => ({
      ...prev,
      conditions: prev.conditions.filter((c) => c.id !== id),
    }));
  }, []);

  /**
   * 更新搜索条件
   */
  const updateSearchCondition = useCallback((
    id: string,
    updates: Partial<SearchCondition>
  ) => {
    setAdvancedSearch((prev) => ({
      ...prev,
      conditions: prev.conditions.map((c) =>
        c.id === id ? { ...c, ...updates } : c
      ),
    }));
  }, []);

  /**
   * 设置时间范围
   */
  const setTimeRange = useCallback((
    timeRange: Partial<AdvancedSearchConfig["timeRange"]>
  ) => {
    setAdvancedSearch((prev) => ({
      ...prev,
      timeRange: { ...prev.timeRange, ...timeRange },
    }));
  }, []);

  /**
   * 应用高级搜索
   */
  const applyAdvancedSearch = useCallback(() => {
    setIsAdvancedSearchActive(true);
  }, []);

  /**
   * 重置高级搜索
   */
  const resetAdvancedSearch = useCallback(() => {
    setAdvancedSearch({
      conditions: [],
      logicOperator: "AND",
      timeRange: {
        enabled: false,
        start: "",
        end: "",
      },
    });
    setIsAdvancedSearchActive(false);
  }, []);

  /**
   * 检查单个日志条目是否匹配搜索条件
   */
  const checkLogMatchesCondition = useCallback((
    log: LogEntry,
    condition: SearchCondition
  ): boolean => {
    if (!condition.value.trim()) return true;

    let logValue = "";

    // 获取要检查的字段值
    switch (condition.field) {
      case "message":
        logValue = log.message;
        break;
      case "module":
        logValue = log.module;
        break;
      case "category":
        logValue = log.category;
        break;
      case "threadId":
        logValue = log.threadId;
        break;
      case "level":
        logValue = log.level;
        break;
      case "autoCategory":
        logValue = log.autoCategory;
        break;
      case "timestamp":
        logValue = log.timestamp;
        break;
      default:
        return true;
    }

    // 处理大小写敏感性
    const searchValue = condition.caseSensitive
      ? condition.value
      : condition.value.toLowerCase();
    const targetValue = condition.caseSensitive
      ? logValue
      : logValue.toLowerCase();

    // 根据操作符进行匹配
    switch (condition.operator) {
      case "contains":
        return targetValue.includes(searchValue);
      case "not_contains":
        return !targetValue.includes(searchValue);
      case "equals":
        return targetValue === searchValue;
      case "not_equals":
        return targetValue !== searchValue;
      case "starts_with":
        return targetValue.startsWith(searchValue);
      case "ends_with":
        return targetValue.endsWith(searchValue);
      case "greater_than":
        return parseFloat(targetValue) > parseFloat(searchValue);
      case "less_than":
        return parseFloat(targetValue) < parseFloat(searchValue);
      case "greater_equal":
        return parseFloat(targetValue) >= parseFloat(searchValue);
      case "less_equal":
        return parseFloat(targetValue) <= parseFloat(searchValue);
      case "in":
        const inValues = searchValue.split(',').map(v => v.trim());
        return inValues.includes(targetValue);
      case "not_in":
        const notInValues = searchValue.split(',').map(v => v.trim());
        return !notInValues.includes(targetValue);
      case "before":
        return new Date(targetValue) < new Date(searchValue);
      case "after":
        return new Date(targetValue) > new Date(searchValue);
      case "between":
        const [startTime, endTime] = searchValue.split(',');
        const logTime = new Date(targetValue);
        return logTime >= new Date(startTime) && logTime <= new Date(endTime);
      default:
        return true;
    }
  }, []);

  /**
   * 检查日志是否在时间范围内
   */
  const checkLogInTimeRange = useCallback((
    log: LogEntry,
    timeRange: AdvancedSearchConfig["timeRange"]
  ): boolean => {
    if (!timeRange.enabled) return true;

    try {
      const logTime = new Date(log.timestamp).getTime();
      const startTime = timeRange.start
        ? new Date(timeRange.start).getTime()
        : 0;
      const endTime = timeRange.end
        ? new Date(timeRange.end).getTime()
        : Date.now();

      return logTime >= startTime && logTime <= endTime;
    } catch (error) {
      // 如果时间解析失败，默认不过滤
      console.warn("时间范围过滤失败:", error);
      return true;
    }
  }, []);

  /**
   * 高级搜索过滤逻辑
   */
  const applyAdvancedFilter = useCallback((logs: LogEntry[]): LogEntry[] => {
    if (!isAdvancedSearchActive) {
      return logs;
    }

    return logs.filter((log) => {
      // 检查时间范围
      if (!checkLogInTimeRange(log, advancedSearch.timeRange)) {
        return false;
      }

      // 如果没有搜索条件，只检查时间范围
      if (advancedSearch.conditions.length === 0) {
        return true;
      }

      // 检查搜索条件
      const results = advancedSearch.conditions.map((condition) =>
        checkLogMatchesCondition(log, condition)
      );

      // 使用AND逻辑：所有条件都必须满足
      return results.every((r) => r);
    });
  }, [
    isAdvancedSearchActive,
    advancedSearch,
    checkLogMatchesCondition,
    checkLogInTimeRange,
  ]);

  /**
   * 计算有效的搜索条件数量
   */
  const validConditionsCount = useMemo(() => {
    return advancedSearch.conditions.filter(c => c.value.trim()).length;
  }, [advancedSearch.conditions]);

  /**
   * 检查是否有有效的搜索配置
   */
  const hasValidSearchConfig = useMemo(() => {
    return validConditionsCount > 0 || advancedSearch.timeRange.enabled;
  }, [validConditionsCount, advancedSearch.timeRange.enabled]);

  return {
    // 搜索配置状态
    advancedSearch,
    isAdvancedSearchActive,
    
    // 搜索条件管理
    addSearchCondition,
    removeSearchCondition,
    updateSearchCondition,
    
    // 搜索配置管理
    setTimeRange,
    
    // 搜索操作
    applyAdvancedSearch,
    resetAdvancedSearch,
    
    // 过滤逻辑
    applyAdvancedFilter,
  };
}; 